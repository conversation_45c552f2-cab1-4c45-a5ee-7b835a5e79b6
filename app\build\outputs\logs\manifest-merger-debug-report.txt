-- Merging decision tree log ---
provider#androidx.core.content.FileProvider
INJECTED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:31:9-39:20
	android:grantUriPermissions
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:35:17-51
	android:authorities
		INJECTED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:33:17-64
	android:exported
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:34:17-41
	android:name
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:32:17-66
manifest
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:2:1-102:12
INJECTED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:2:1-102:12
INJECTED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:2:1-102:12
INJECTED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:2:1-102:12
MERGED from [androidx.databinding:viewbinding:8.5.2] C:\Users\<USER>\.gradle\caches\transforms-4\5f7a0f8bc0449c7f4cfbab25eb4ecf71\transformed\jetified-viewbinding-8.5.2\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.leakcanary:leakcanary-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\4da4f441a9fb63d0ab4ee08634b7cf49\transformed\jetified-leakcanary-android-2.10\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\56b21b1e2df5e7a9e2bbd796d8a365f1\transformed\material-1.12.0\AndroidManifest.xml:17:1-24:12
MERGED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:2:1-33:12
MERGED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:2:1-35:12
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\a4f196f9d3d2b9c2d0814211c7c04955\transformed\constraintlayout-2.1.4\AndroidManifest.xml:2:1-11:12
MERGED from [com.github.QuadFlask:colorpicker:0.0.13] C:\Users\<USER>\.gradle\caches\transforms-4\d401c5c3b0dac5d91e2ca9d2044972ce\transformed\jetified-colorpicker-0.0.13\AndroidManifest.xml:2:1-14:12
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\1052397ee5b8e5745735d4b082350b38\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.preference:preference-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\f64af4fcec0833398cf7f56d3f0e55a4\transformed\jetified-preference-ktx-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\c4e3970f58cff9116df172ae1e89ec5d\transformed\preference-1.2.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\4886f4714567b788bedb48de3c472517\transformed\appcompat-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\fd1840ee370e9f46a307d715ea24ff82\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\1c6e13e74ed25f6edfeeec81c2400dab\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d21d60fa257ceb9183d742ea34b7b496\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.bumptech.glide:glide:4.14.2] C:\Users\<USER>\.gradle\caches\transforms-4\172a0f485d03a0503cb3de3b60c167ba\transformed\jetified-glide-4.14.2\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-4\097237b8b9cdd7b3c25214a2e00eebf6\transformed\recyclerview-1.3.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\transforms-4\859b5441f9fbe333b8f29647fda22bbf\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-ads:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6a58776e9dcdced13d931fd31f07cadf\transformed\jetified-play-services-ads-24.3.0\AndroidManifest.xml:17:1-25:12
MERGED from [com.google.android.gms:play-services-pal:20.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\7b74f0eaa4e0c15c84b817fd728d73ae\transformed\jetified-play-services-pal-20.0.1\AndroidManifest.xml:2:1-11:12
MERGED from [com.google.android.gms:play-services-ads-identifier:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\61cbbd69572351bdea480ebf9f266fca\transformed\jetified-play-services-ads-identifier-18.2.0\AndroidManifest.xml:17:1-27:12
MERGED from [com.google.android.gms:play-services-appset:16.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\ef5400b6d085975012851d2eb92c79dc\transformed\jetified-play-services-appset-16.0.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8d70832d7f1deb228721870d2bb07788\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f650564017396edffb38f65178e76876\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:2:1-5:12
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:17:1-115:12
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-4\80a65e5aed6e155b189f4dcb10fd10f6\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:17:1-28:12
MERGED from [com.google.android.ump:user-messaging-platform:3.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\45e28ea886728f6c9521180bfe880361\transformed\jetified-user-messaging-platform-3.2.0\AndroidManifest.xml:2:1-7:12
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-4\1234cefec33ce737c21211a16c1400b3\transformed\jetified-play-services-measurement-base-20.1.2\AndroidManifest.xml:2:1-9:12
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\acab93b1b19a0a7fc9f8e2a900142b63\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.fragment:fragment-ktx:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-4\b84e674a21560e946203cea650c7d3ac\transformed\jetified-fragment-ktx-1.3.6\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\5f6a47ec2bdb8f8ffa1652b4c9a65bf6\transformed\fragment-1.5.4\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\d8178f82324e7d512be64a7e4f221deb\transformed\jetified-activity-ktx-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\59bfd416416883deea0bb727308834ce\transformed\jetified-activity-1.8.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\ac185097d874e47ec4cafd044e6cc8ed\transformed\browser-1.8.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\11557ed537c408f8869650a01b4f980f\transformed\media-1.7.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ba6177f39b481fae6ce909f54880846d\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\a982044848a2565039a080a184b32318\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\b8c8b9328e3c6d7235dc1272a27e9b0d\transformed\transition-1.5.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6867eb619e543b1744faf8ce1403a87c\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce533092d1d1e75a0c49ae6325e04e7d\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\72279248dc3cf56767593da2dba462d5\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\41ddcd0b7a28586eb05341e8f362bcb9\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\bfe68e5eb7521661e8a6468894cca0cd\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:17:1-28:12
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\8b4eb601851c1ba54121a6e8ebe11709\transformed\jetified-core-ktx-1.13.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\2b2c6ad2b7afb142c2f8cb4d0363526e\transformed\drawerlayout-1.1.1\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\9d251b9d8ce7bc6804eabd762524a58b\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\7802f02953c2e3e2eff05f589482e894\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\36304a4266596246e7b74ddac37af8fc\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] C:\Users\<USER>\.gradle\caches\transforms-4\e9e2b5177130b9efdbb326a781983289\transformed\webkit-1.11.0-alpha02\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.work:work-runtime-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\4ade8fa3d445b09822b893727187cf1a\transformed\work-runtime-ktx-2.9.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:17:1-145:12
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b6ef67333f122be511bcffd14dc5db2b\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\578949a8575fb0bb492c22f660ef02da\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b9a226ba4c83feb929f659f51c832a78\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6eef0a3277bec9d1314385c57058a2f4\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\6024827ae34f7aff50bae9e3e0186c8f\transformed\customview-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f956628949a3250544a83e00b93b077\transformed\jetified-window-1.0.0\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\c3a5dd56922f0d9d5fe7ff90c31db109\transformed\core-1.13.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\407ce014fdfcad4f3d05c3e44277f809\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\d7c2bfbb5af45d887fc66aa48a8a5cea\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-service:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\54893f0ffc2693bcfde8b4ab83dcd91b\transformed\jetified-lifecycle-service-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\951de76ebfa8d163b826735331969649\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\17483682e8bf057c908a5c006a649d06\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\2b16a6eb20122dc2eeafe98251b8dc54\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:2:1-5:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\346ef79e87f08ad9fc9965b50336a7de\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\5e1906c59cfb2b087b275ac0bd19cff8\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\2e1f7bb515f0604d4833bd7753ae485e\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\5c0c6f72ed21f06b5bf9de1c3611e04f\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\ecd49d5b530cba4f5c8f35f681951d1f\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:17:1-35:12
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:17:1-105:12
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\ae4c90741e6883f9dd637e00a9aec732\transformed\jetified-leakcanary-object-watcher-android-2.10\AndroidManifest.xml:2:1-15:12
MERGED from [com.squareup.leakcanary:plumber-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\979f7e3a02700746ad02a2232efd5b5b\transformed\jetified-plumber-android-2.10\AndroidManifest.xml:2:1-15:12
MERGED from [androidx.room:room-ktx:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\d6b4ccd6fdde0e6b89402dbb066bb633\transformed\jetified-room-ktx-2.5.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\055a1ceb01c3036c4aaf523a4c606cb3\transformed\room-runtime-2.5.0\AndroidManifest.xml:17:1-31:12
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\f1f10f1f7c3efa76f67976899402a483\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\b877b5607c2ac5211a62838db5a6ea86\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\cd5f7a6a37fcef9b566cc0298fdb29d5\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-androidx:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\b0f2f2b1ea683d07cca6c88eea7d04d4\transformed\jetified-leakcanary-object-watcher-android-androidx-2.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-support-fragments:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\b89b225644856518cebb3f492eb263ac\transformed\jetified-leakcanary-object-watcher-android-support-fragments-2.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\bc29dcba26332e73155eb74df0ec32ab\transformed\jetified-leakcanary-object-watcher-android-core-2.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.leakcanary:plumber-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\b22f2d36704a10d6d0c9b55e100d666f\transformed\jetified-plumber-android-core-2.10\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e7ca046154a3cdfdc427cab1ab602fbc\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.squareup.leakcanary:leakcanary-android-utils:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\0d78391c2760c4537eda9077bb4f87b7\transformed\jetified-leakcanary-android-utils-2.10\AndroidManifest.xml:2:1-7:12
MERGED from [com.squareup.curtains:curtains:1.2.4] C:\Users\<USER>\.gradle\caches\transforms-4\00f99faa29e9b9558ed7a4323dc949b4\transformed\jetified-curtains-1.2.4\AndroidManifest.xml:2:1-9:12
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\1b67ab735444fe9da40fb99582ba89ec\transformed\sqlite-2.3.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c886296f8a815b83add004e4ea2b670\transformed\cardview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\e8b3344ea7319a7021dd2922924f5d55\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\92e7a0203c6e4f0920aa306c34c2c22a\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\20793f5baa1d05e9d84b902adf529651\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:17:1-55:12
MERGED from [com.github.bumptech.glide:gifdecoder:4.14.2] C:\Users\<USER>\.gradle\caches\transforms-4\d14492c9e44ff7606142ae00f753b205\transformed\jetified-gifdecoder-4.14.2\AndroidManifest.xml:2:1-11:12
MERGED from [androidx.exifinterface:exifinterface:1.3.3] C:\Users\<USER>\.gradle\caches\transforms-4\9dacbf323299d6786d635444d15b2ae6\transformed\exifinterface-1.3.3\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\21b516116edda89f30d8dcb441874489\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e8217cf4d1379041f0d649c39ee3f1c9\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\64e5f73b398dfbd6c1519db09deba5ab\transformed\documentfile-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7bfced2e1a7b76bf07801170f8f677e7\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\05705b555e34d28fd179efd453db9fdb\transformed\print-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\6a25bde3cfc6c0bc2e5556a7b2e98230\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:15:1-37:12
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\891a814344d0e09ae3597ffe3da67f30\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:15:1-41:12
MERGED from [com.google.android.datatransport:transport-api:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4969a8d10287214ceabd1fb5ee380b8e\transformed\jetified-transport-api-3.0.0\AndroidManifest.xml:15:1-22:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\1bc7564f966159972dc6d67fd09a85b9\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\af472de109f8654b50e8e056af3f9442\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:15:1-23:12
MERGED from [:indicatorseekbar] D:\Android\KidsDrawingApp\indicatorseekbar\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:2:1-7:12
MERGED from [com.github.yukuku:ambilwarna:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ff9bd2009e5ad31b6e19abe7b702bae\transformed\jetified-ambilwarna-2.0.1\AndroidManifest.xml:2:1-12:12
MERGED from [com.intuit.sdp:sdp-android:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\cd5880d74125cf1efbbb9940d7720254\transformed\jetified-sdp-android-1.1.0\AndroidManifest.xml:2:1-11:12
	package
		INJECTED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:3:9-55
	android:versionCode
		INJECTED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.ACCESS_NETWORK_STATE
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:5:5-78
MERGED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:19:5-79
MERGED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:19:5-79
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:25:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-4\80a65e5aed6e155b189f4dcb10fd10f6\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-4\80a65e5aed6e155b189f4dcb10fd10f6\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:24:5-79
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\6a25bde3cfc6c0bc2e5556a7b2e98230\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\6a25bde3cfc6c0bc2e5556a7b2e98230\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:24:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\891a814344d0e09ae3597ffe3da67f30\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:22:5-79
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\891a814344d0e09ae3597ffe3da67f30\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:22:5-79
	android:name
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:5:22-76
uses-permission#android.permission.INTERNET
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:6:5-66
MERGED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:12:5-67
MERGED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:12:5-67
MERGED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:18:5-67
MERGED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:18:5-67
MERGED from [com.google.android.gms:play-services-pal:20.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\7b74f0eaa4e0c15c84b817fd728d73ae\transformed\jetified-play-services-pal-20.0.1\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-pal:20.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\7b74f0eaa4e0c15c84b817fd728d73ae\transformed\jetified-play-services-pal-20.0.1\AndroidManifest.xml:8:5-67
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:24:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-4\80a65e5aed6e155b189f4dcb10fd10f6\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-4\80a65e5aed6e155b189f4dcb10fd10f6\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:23:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\6a25bde3cfc6c0bc2e5556a7b2e98230\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:25:5-67
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\6a25bde3cfc6c0bc2e5556a7b2e98230\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:25:5-67
	android:name
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:6:22-64
uses-permission#com.google.android.gms.permission.AD_ID
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:7:5-78
MERGED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:13:5-79
MERGED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:13:5-79
MERGED from [com.google.android.gms:play-services-pal:20.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\7b74f0eaa4e0c15c84b817fd728d73ae\transformed\jetified-play-services-pal-20.0.1\AndroidManifest.xml:9:5-79
MERGED from [com.google.android.gms:play-services-pal:20.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\7b74f0eaa4e0c15c84b817fd728d73ae\transformed\jetified-play-services-pal-20.0.1\AndroidManifest.xml:9:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\61cbbd69572351bdea480ebf9f266fca\transformed\jetified-play-services-ads-identifier-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-identifier:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\61cbbd69572351bdea480ebf9f266fca\transformed\jetified-play-services-ads-identifier-18.2.0\AndroidManifest.xml:23:5-79
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-4\80a65e5aed6e155b189f4dcb10fd10f6\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:26:5-79
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-4\80a65e5aed6e155b189f4dcb10fd10f6\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:26:5-79
	android:name
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:7:22-76
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:9:5-10:38
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:25:5-80
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:25:5-80
	android:maxSdkVersion
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:10:9-35
	android:name
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:9:22-77
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:11:5-12:38
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:26:5-81
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:26:5-81
	android:maxSdkVersion
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:12:9-35
	android:name
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:11:22-78
application
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:14:5-100:19
INJECTED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:14:5-100:19
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\56b21b1e2df5e7a9e2bbd796d8a365f1\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\56b21b1e2df5e7a9e2bbd796d8a365f1\transformed\material-1.12.0\AndroidManifest.xml:22:5-20
MERGED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:31:5-58
MERGED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:31:5-58
MERGED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:27:5-33:19
MERGED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:27:5-33:19
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\a4f196f9d3d2b9c2d0814211c7c04955\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\a4f196f9d3d2b9c2d0814211c7c04955\transformed\constraintlayout-2.1.4\AndroidManifest.xml:9:5-20
MERGED from [com.github.QuadFlask:colorpicker:0.0.13] C:\Users\<USER>\.gradle\caches\transforms-4\d401c5c3b0dac5d91e2ca9d2044972ce\transformed\jetified-colorpicker-0.0.13\AndroidManifest.xml:12:5-20
MERGED from [com.github.QuadFlask:colorpicker:0.0.13] C:\Users\<USER>\.gradle\caches\transforms-4\d401c5c3b0dac5d91e2ca9d2044972ce\transformed\jetified-colorpicker-0.0.13\AndroidManifest.xml:12:5-20
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\1c6e13e74ed25f6edfeeec81c2400dab\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\1c6e13e74ed25f6edfeeec81c2400dab\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [com.github.bumptech.glide:glide:4.14.2] C:\Users\<USER>\.gradle\caches\transforms-4\172a0f485d03a0503cb3de3b60c167ba\transformed\jetified-glide-4.14.2\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:glide:4.14.2] C:\Users\<USER>\.gradle\caches\transforms-4\172a0f485d03a0503cb3de3b60c167ba\transformed\jetified-glide-4.14.2\AndroidManifest.xml:9:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\61cbbd69572351bdea480ebf9f266fca\transformed\jetified-play-services-ads-identifier-18.2.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-ads-identifier:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\61cbbd69572351bdea480ebf9f266fca\transformed\jetified-play-services-ads-identifier-18.2.0\AndroidManifest.xml:25:5-20
MERGED from [com.google.android.gms:play-services-appset:16.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\ef5400b6d085975012851d2eb92c79dc\transformed\jetified-play-services-appset-16.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-appset:16.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\ef5400b6d085975012851d2eb92c79dc\transformed\jetified-play-services-appset-16.0.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8d70832d7f1deb228721870d2bb07788\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8d70832d7f1deb228721870d2bb07788\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:4:5-6:19
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f650564017396edffb38f65178e76876\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f650564017396edffb38f65178e76876\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:4:5-20
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:70:5-113:19
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:70:5-113:19
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-4\1234cefec33ce737c21211a16c1400b3\transformed\jetified-play-services-measurement-base-20.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-4\1234cefec33ce737c21211a16c1400b3\transformed\jetified-play-services-measurement-base-20.1.2\AndroidManifest.xml:7:5-20
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\acab93b1b19a0a7fc9f8e2a900142b63\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\acab93b1b19a0a7fc9f8e2a900142b63\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:5:5-7:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\bfe68e5eb7521661e8a6468894cca0cd\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\bfe68e5eb7521661e8a6468894cca0cd\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:22:5-26:19
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:28:5-143:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f956628949a3250544a83e00b93b077\transformed\jetified-window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f956628949a3250544a83e00b93b077\transformed\jetified-window-1.0.0\AndroidManifest.xml:24:5-31:19
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\c3a5dd56922f0d9d5fe7ff90c31db109\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\c3a5dd56922f0d9d5fe7ff90c31db109\transformed\core-1.13.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\ecd49d5b530cba4f5c8f35f681951d1f\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\ecd49d5b530cba4f5c8f35f681951d1f\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:23:5-33:19
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:31:5-103:19
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:31:5-103:19
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\ae4c90741e6883f9dd637e00a9aec732\transformed\jetified-leakcanary-object-watcher-android-2.10\AndroidManifest.xml:7:5-13:19
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\ae4c90741e6883f9dd637e00a9aec732\transformed\jetified-leakcanary-object-watcher-android-2.10\AndroidManifest.xml:7:5-13:19
MERGED from [com.squareup.leakcanary:plumber-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\979f7e3a02700746ad02a2232efd5b5b\transformed\jetified-plumber-android-2.10\AndroidManifest.xml:7:5-13:19
MERGED from [com.squareup.leakcanary:plumber-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\979f7e3a02700746ad02a2232efd5b5b\transformed\jetified-plumber-android-2.10\AndroidManifest.xml:7:5-13:19
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\055a1ceb01c3036c4aaf523a4c606cb3\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\055a1ceb01c3036c4aaf523a4c606cb3\transformed\room-runtime-2.5.0\AndroidManifest.xml:23:5-29:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\e8b3344ea7319a7021dd2922924f5d55\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\e8b3344ea7319a7021dd2922924f5d55\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:23:5-53:19
MERGED from [com.github.bumptech.glide:gifdecoder:4.14.2] C:\Users\<USER>\.gradle\caches\transforms-4\d14492c9e44ff7606142ae00f753b205\transformed\jetified-gifdecoder-4.14.2\AndroidManifest.xml:9:5-20
MERGED from [com.github.bumptech.glide:gifdecoder:4.14.2] C:\Users\<USER>\.gradle\caches\transforms-4\d14492c9e44ff7606142ae00f753b205\transformed\jetified-gifdecoder-4.14.2\AndroidManifest.xml:9:5-20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\21b516116edda89f30d8dcb441874489\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\21b516116edda89f30d8dcb441874489\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\6a25bde3cfc6c0bc2e5556a7b2e98230\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\6a25bde3cfc6c0bc2e5556a7b2e98230\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:27:5-35:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\891a814344d0e09ae3597ffe3da67f30\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:25:5-39:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\891a814344d0e09ae3597ffe3da67f30\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:25:5-39:19
	android:extractNativeLibs
		INJECTED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml
	android:requestLegacyExternalStorage
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:19:13-56
	tools:ignore
		ADDED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:31:18-55
	android:roundIcon
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:21:13-58
	android:largeHeap
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:25:13-37
	android:icon
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:20:13-47
	android:fullBackupOnly
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:18:13-43
	android:appComponentFactory
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\c3a5dd56922f0d9d5fe7ff90c31db109\transformed\core-1.13.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:23:13-39
	android:label
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:22:13-45
	android:hardwareAccelerated
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:27:13-47
	android:fullBackupContent
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:17:13-46
	android:allowBackup
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:16:13-40
	android:theme
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:24:13-44
	tools:replace
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:28:13-48
	android:usesCleartextTraffic
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:26:13-48
	android:name
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:15:13-49
activity#drawing.jaraappskids.kidsdrawing.activities.SplashActivity
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:41:9-51:20
	android:exported
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:42:13-36
	android:theme
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:44:17-55
	android:name
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:43:17-58
intent-filter#action:name:android.intent.action.MAIN+action:name:android.intent.action.VIEW+category:name:android.intent.category.LAUNCHER
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:45:13-50:29
action#android.intent.action.MAIN
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:46:17-68
	android:name
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:46:25-66
action#android.intent.action.VIEW
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:47:17-68
	android:name
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:47:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:49:17-76
	android:name
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:49:27-74
activity#drawing.jaraappskids.kidsdrawing.activities.MainActivity
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:53:9-56:19
	android:screenOrientation
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:55:17-53
	android:name
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:54:17-56
activity#drawing.jaraappskids.kidsdrawing.activities.BGImageListActivity
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:58:9-61:19
	android:screenOrientation
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:60:17-53
	android:name
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:59:17-63
activity#drawing.jaraappskids.kidsdrawing.activities.EditorActivity
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:63:9-67:19
	android:screenOrientation
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:65:17-53
	android:windowSoftInputMode
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:66:17-60
	android:name
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:64:17-58
activity#drawing.jaraappskids.kidsdrawing.mywork.MyWorkActivity
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:69:9-71:55
	android:screenOrientation
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:71:17-53
	android:name
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:70:17-54
activity#drawing.jaraappskids.kidsdrawing.mywork.FullScreenActivity
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:72:9-74:55
	android:screenOrientation
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:74:17-53
	android:name
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:73:17-58
meta-data#com.google.android.gms.ads.APPLICATION_ID
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:76:9-78:65
	android:value
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:78:17-62
	android:name
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:77:17-73
meta-data#com.google.android.gms.version
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:80:9-82:73
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\acab93b1b19a0a7fc9f8e2a900142b63\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\acab93b1b19a0a7fc9f8e2a900142b63\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:6:9-122
	android:value
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:82:17-70
	android:name
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:81:17-62
property#android.adservices.AD_SERVICES_CONFIG
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:84:9-87:48
	android:resource
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:86:13-59
	tools:replace
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:87:13-45
	android:name
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:85:13-65
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:36:13-38:57
	android:resource
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:38:21-55
	android:name
		ADDED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml:37:21-71
uses-sdk
INJECTED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml
INJECTED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml
MERGED from [androidx.databinding:viewbinding:8.5.2] C:\Users\<USER>\.gradle\caches\transforms-4\5f7a0f8bc0449c7f4cfbab25eb4ecf71\transformed\jetified-viewbinding-8.5.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.databinding:viewbinding:8.5.2] C:\Users\<USER>\.gradle\caches\transforms-4\5f7a0f8bc0449c7f4cfbab25eb4ecf71\transformed\jetified-viewbinding-8.5.2\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\4da4f441a9fb63d0ab4ee08634b7cf49\transformed\jetified-leakcanary-android-2.10\AndroidManifest.xml:20:5-44
MERGED from [com.squareup.leakcanary:leakcanary-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\4da4f441a9fb63d0ab4ee08634b7cf49\transformed\jetified-leakcanary-android-2.10\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\56b21b1e2df5e7a9e2bbd796d8a365f1\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.material:material:1.12.0] C:\Users\<USER>\.gradle\caches\transforms-4\56b21b1e2df5e7a9e2bbd796d8a365f1\transformed\material-1.12.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:8:5-10:61
MERGED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:8:5-10:61
MERGED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:7:5-9:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\a4f196f9d3d2b9c2d0814211c7c04955\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.constraintlayout:constraintlayout:2.1.4] C:\Users\<USER>\.gradle\caches\transforms-4\a4f196f9d3d2b9c2d0814211c7c04955\transformed\constraintlayout-2.1.4\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.QuadFlask:colorpicker:0.0.13] C:\Users\<USER>\.gradle\caches\transforms-4\d401c5c3b0dac5d91e2ca9d2044972ce\transformed\jetified-colorpicker-0.0.13\AndroidManifest.xml:8:5-10:41
MERGED from [com.github.QuadFlask:colorpicker:0.0.13] C:\Users\<USER>\.gradle\caches\transforms-4\d401c5c3b0dac5d91e2ca9d2044972ce\transformed\jetified-colorpicker-0.0.13\AndroidManifest.xml:8:5-10:41
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\1052397ee5b8e5745735d4b082350b38\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat-resources:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\1052397ee5b8e5745735d4b082350b38\transformed\jetified-appcompat-resources-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.preference:preference-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\f64af4fcec0833398cf7f56d3f0e55a4\transformed\jetified-preference-ktx-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\f64af4fcec0833398cf7f56d3f0e55a4\transformed\jetified-preference-ktx-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\c4e3970f58cff9116df172ae1e89ec5d\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.preference:preference:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\c4e3970f58cff9116df172ae1e89ec5d\transformed\preference-1.2.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\4886f4714567b788bedb48de3c472517\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.appcompat:appcompat:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\4886f4714567b788bedb48de3c472517\transformed\appcompat-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\fd1840ee370e9f46a307d715ea24ff82\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2-views-helper:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\fd1840ee370e9f46a307d715ea24ff82\transformed\jetified-emoji2-views-helper-1.3.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\1c6e13e74ed25f6edfeeec81c2400dab\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\1c6e13e74ed25f6edfeeec81c2400dab\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d21d60fa257ceb9183d742ea34b7b496\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-v4:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\d21d60fa257ceb9183d742ea34b7b496\transformed\legacy-support-v4-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.bumptech.glide:glide:4.14.2] C:\Users\<USER>\.gradle\caches\transforms-4\172a0f485d03a0503cb3de3b60c167ba\transformed\jetified-glide-4.14.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:glide:4.14.2] C:\Users\<USER>\.gradle\caches\transforms-4\172a0f485d03a0503cb3de3b60c167ba\transformed\jetified-glide-4.14.2\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-4\097237b8b9cdd7b3c25214a2e00eebf6\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.recyclerview:recyclerview:1.3.2] C:\Users\<USER>\.gradle\caches\transforms-4\097237b8b9cdd7b3c25214a2e00eebf6\transformed\recyclerview-1.3.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\transforms-4\859b5441f9fbe333b8f29647fda22bbf\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [androidx.viewpager2:viewpager2:1.1.0-beta02] C:\Users\<USER>\.gradle\caches\transforms-4\859b5441f9fbe333b8f29647fda22bbf\transformed\jetified-viewpager2-1.1.0-beta02\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-ads:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6a58776e9dcdced13d931fd31f07cadf\transformed\jetified-play-services-ads-24.3.0\AndroidManifest.xml:21:5-23:52
MERGED from [com.google.android.gms:play-services-ads:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6a58776e9dcdced13d931fd31f07cadf\transformed\jetified-play-services-ads-24.3.0\AndroidManifest.xml:21:5-23:52
MERGED from [com.google.android.gms:play-services-pal:20.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\7b74f0eaa4e0c15c84b817fd728d73ae\transformed\jetified-play-services-pal-20.0.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-pal:20.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\7b74f0eaa4e0c15c84b817fd728d73ae\transformed\jetified-play-services-pal-20.0.1\AndroidManifest.xml:6:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\61cbbd69572351bdea480ebf9f266fca\transformed\jetified-play-services-ads-identifier-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-ads-identifier:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\61cbbd69572351bdea480ebf9f266fca\transformed\jetified-play-services-ads-identifier-18.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-appset:16.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\ef5400b6d085975012851d2eb92c79dc\transformed\jetified-play-services-appset-16.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-appset:16.0.2] C:\Users\<USER>\.gradle\caches\transforms-4\ef5400b6d085975012851d2eb92c79dc\transformed\jetified-play-services-appset-16.0.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8d70832d7f1deb228721870d2bb07788\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8d70832d7f1deb228721870d2bb07788\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f650564017396edffb38f65178e76876\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-tasks:18.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\f650564017396edffb38f65178e76876\transformed\jetified-play-services-tasks-18.2.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:21:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-4\80a65e5aed6e155b189f4dcb10fd10f6\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-4\80a65e5aed6e155b189f4dcb10fd10f6\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.ump:user-messaging-platform:3.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\45e28ea886728f6c9521180bfe880361\transformed\jetified-user-messaging-platform-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.ump:user-messaging-platform:3.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\45e28ea886728f6c9521180bfe880361\transformed\jetified-user-messaging-platform-3.2.0\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-4\1234cefec33ce737c21211a16c1400b3\transformed\jetified-play-services-measurement-base-20.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-measurement-base:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-4\1234cefec33ce737c21211a16c1400b3\transformed\jetified-play-services-measurement-base-20.1.2\AndroidManifest.xml:5:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\acab93b1b19a0a7fc9f8e2a900142b63\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [com.google.android.gms:play-services-basement:18.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\acab93b1b19a0a7fc9f8e2a900142b63\transformed\jetified-play-services-basement-18.4.0\AndroidManifest.xml:3:5-44
MERGED from [androidx.fragment:fragment-ktx:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-4\b84e674a21560e946203cea650c7d3ac\transformed\jetified-fragment-ktx-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment-ktx:1.3.6] C:\Users\<USER>\.gradle\caches\transforms-4\b84e674a21560e946203cea650c7d3ac\transformed\jetified-fragment-ktx-1.3.6\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\5f6a47ec2bdb8f8ffa1652b4c9a65bf6\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.5.4] C:\Users\<USER>\.gradle\caches\transforms-4\5f6a47ec2bdb8f8ffa1652b4c9a65bf6\transformed\fragment-1.5.4\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\d8178f82324e7d512be64a7e4f221deb\transformed\jetified-activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity-ktx:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\d8178f82324e7d512be64a7e4f221deb\transformed\jetified-activity-ktx-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\59bfd416416883deea0bb727308834ce\transformed\jetified-activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\59bfd416416883deea0bb727308834ce\transformed\jetified-activity-1.8.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\ac185097d874e47ec4cafd044e6cc8ed\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.browser:browser:1.8.0] C:\Users\<USER>\.gradle\caches\transforms-4\ac185097d874e47ec4cafd044e6cc8ed\transformed\browser-1.8.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\11557ed537c408f8869650a01b4f980f\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.media:media:1.7.0] C:\Users\<USER>\.gradle\caches\transforms-4\11557ed537c408f8869650a01b4f980f\transformed\media-1.7.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ba6177f39b481fae6ce909f54880846d\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-ui:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ba6177f39b481fae6ce909f54880846d\transformed\legacy-support-core-ui-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\a982044848a2565039a080a184b32318\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.slidingpanelayout:slidingpanelayout:1.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\a982044848a2565039a080a184b32318\transformed\slidingpanelayout-1.2.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\b8c8b9328e3c6d7235dc1272a27e9b0d\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.transition:transition:1.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\b8c8b9328e3c6d7235dc1272a27e9b0d\transformed\transition-1.5.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6867eb619e543b1744faf8ce1403a87c\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.dynamicanimation:dynamicanimation:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6867eb619e543b1744faf8ce1403a87c\transformed\dynamicanimation-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce533092d1d1e75a0c49ae6325e04e7d\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.legacy:legacy-support-core-utils:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\ce533092d1d1e75a0c49ae6325e04e7d\transformed\legacy-support-core-utils-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\72279248dc3cf56767593da2dba462d5\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\72279248dc3cf56767593da2dba462d5\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\41ddcd0b7a28586eb05341e8f362bcb9\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices-java:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\41ddcd0b7a28586eb05341e8f362bcb9\transformed\jetified-ads-adservices-java-1.0.0-beta05\AndroidManifest.xml:5:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\bfe68e5eb7521661e8a6468894cca0cd\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\bfe68e5eb7521661e8a6468894cca0cd\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\8b4eb601851c1ba54121a6e8ebe11709\transformed\jetified-core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\8b4eb601851c1ba54121a6e8ebe11709\transformed\jetified-core-ktx-1.13.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\2b2c6ad2b7afb142c2f8cb4d0363526e\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\2b2c6ad2b7afb142c2f8cb4d0363526e\transformed\drawerlayout-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\9d251b9d8ce7bc6804eabd762524a58b\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.coordinatorlayout:coordinatorlayout:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\9d251b9d8ce7bc6804eabd762524a58b\transformed\coordinatorlayout-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\7802f02953c2e3e2eff05f589482e894\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\7802f02953c2e3e2eff05f589482e894\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\36304a4266596246e7b74ddac37af8fc\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\36304a4266596246e7b74ddac37af8fc\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] C:\Users\<USER>\.gradle\caches\transforms-4\e9e2b5177130b9efdbb326a781983289\transformed\webkit-1.11.0-alpha02\AndroidManifest.xml:5:5-44
MERGED from [androidx.webkit:webkit:1.11.0-alpha02] C:\Users\<USER>\.gradle\caches\transforms-4\e9e2b5177130b9efdbb326a781983289\transformed\webkit-1.11.0-alpha02\AndroidManifest.xml:5:5-44
MERGED from [androidx.work:work-runtime-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\4ade8fa3d445b09822b893727187cf1a\transformed\work-runtime-ktx-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime-ktx:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\4ade8fa3d445b09822b893727187cf1a\transformed\work-runtime-ktx-2.9.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b6ef67333f122be511bcffd14dc5db2b\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b6ef67333f122be511bcffd14dc5db2b\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\578949a8575fb0bb492c22f660ef02da\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\578949a8575fb0bb492c22f660ef02da\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b9a226ba4c83feb929f659f51c832a78\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.swiperefreshlayout:swiperefreshlayout:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\b9a226ba4c83feb929f659f51c832a78\transformed\swiperefreshlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6eef0a3277bec9d1314385c57058a2f4\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.asynclayoutinflater:asynclayoutinflater:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\6eef0a3277bec9d1314385c57058a2f4\transformed\asynclayoutinflater-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\6024827ae34f7aff50bae9e3e0186c8f\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\6024827ae34f7aff50bae9e3e0186c8f\transformed\customview-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f956628949a3250544a83e00b93b077\transformed\jetified-window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f956628949a3250544a83e00b93b077\transformed\jetified-window-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\c3a5dd56922f0d9d5fe7ff90c31db109\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\c3a5dd56922f0d9d5fe7ff90c31db109\transformed\core-1.13.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\407ce014fdfcad4f3d05c3e44277f809\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\407ce014fdfcad4f3d05c3e44277f809\transformed\jetified-lifecycle-runtime-ktx-release\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\d7c2bfbb5af45d887fc66aa48a8a5cea\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\d7c2bfbb5af45d887fc66aa48a8a5cea\transformed\jetified-lifecycle-viewmodel-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\54893f0ffc2693bcfde8b4ab83dcd91b\transformed\jetified-lifecycle-service-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-service:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\54893f0ffc2693bcfde8b4ab83dcd91b\transformed\jetified-lifecycle-service-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\951de76ebfa8d163b826735331969649\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core-ktx:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\951de76ebfa8d163b826735331969649\transformed\jetified-lifecycle-livedata-core-ktx-2.8.7\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\17483682e8bf057c908a5c006a649d06\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\17483682e8bf057c908a5c006a649d06\transformed\lifecycle-livedata-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\2b16a6eb20122dc2eeafe98251b8dc54\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\2b16a6eb20122dc2eeafe98251b8dc54\transformed\lifecycle-viewmodel-2.8.7\AndroidManifest.xml:4:5-43
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\346ef79e87f08ad9fc9965b50336a7de\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\346ef79e87f08ad9fc9965b50336a7de\transformed\jetified-lifecycle-viewmodel-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\5e1906c59cfb2b087b275ac0bd19cff8\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-android:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\5e1906c59cfb2b087b275ac0bd19cff8\transformed\jetified-lifecycle-runtime-release\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\2e1f7bb515f0604d4833bd7753ae485e\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\2e1f7bb515f0604d4833bd7753ae485e\transformed\jetified-lifecycle-viewmodel-savedstate-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\5c0c6f72ed21f06b5bf9de1c3611e04f\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\5c0c6f72ed21f06b5bf9de1c3611e04f\transformed\lifecycle-livedata-core-2.8.7\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\ecd49d5b530cba4f5c8f35f681951d1f\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\ecd49d5b530cba4f5c8f35f681951d1f\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:21:5-44
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:20:5-22:41
MERGED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:20:5-22:41
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\ae4c90741e6883f9dd637e00a9aec732\transformed\jetified-leakcanary-object-watcher-android-2.10\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\ae4c90741e6883f9dd637e00a9aec732\transformed\jetified-leakcanary-object-watcher-android-2.10\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:plumber-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\979f7e3a02700746ad02a2232efd5b5b\transformed\jetified-plumber-android-2.10\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:plumber-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\979f7e3a02700746ad02a2232efd5b5b\transformed\jetified-plumber-android-2.10\AndroidManifest.xml:5:5-44
MERGED from [androidx.room:room-ktx:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\d6b4ccd6fdde0e6b89402dbb066bb633\transformed\jetified-room-ktx-2.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-ktx:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\d6b4ccd6fdde0e6b89402dbb066bb633\transformed\jetified-room-ktx-2.5.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\055a1ceb01c3036c4aaf523a4c606cb3\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\055a1ceb01c3036c4aaf523a4c606cb3\transformed\room-runtime-2.5.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\f1f10f1f7c3efa76f67976899402a483\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.annotation:annotation-experimental:1.4.0] C:\Users\<USER>\.gradle\caches\transforms-4\f1f10f1f7c3efa76f67976899402a483\transformed\jetified-annotation-experimental-1.4.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\b877b5607c2ac5211a62838db5a6ea86\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\b877b5607c2ac5211a62838db5a6ea86\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\cd5f7a6a37fcef9b566cc0298fdb29d5\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] C:\Users\<USER>\.gradle\caches\transforms-4\cd5f7a6a37fcef9b566cc0298fdb29d5\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-androidx:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\b0f2f2b1ea683d07cca6c88eea7d04d4\transformed\jetified-leakcanary-object-watcher-android-androidx-2.10\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-androidx:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\b0f2f2b1ea683d07cca6c88eea7d04d4\transformed\jetified-leakcanary-object-watcher-android-androidx-2.10\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-support-fragments:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\b89b225644856518cebb3f492eb263ac\transformed\jetified-leakcanary-object-watcher-android-support-fragments-2.10\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-support-fragments:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\b89b225644856518cebb3f492eb263ac\transformed\jetified-leakcanary-object-watcher-android-support-fragments-2.10\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\bc29dcba26332e73155eb74df0ec32ab\transformed\jetified-leakcanary-object-watcher-android-core-2.10\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-object-watcher-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\bc29dcba26332e73155eb74df0ec32ab\transformed\jetified-leakcanary-object-watcher-android-core-2.10\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:plumber-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\b22f2d36704a10d6d0c9b55e100d666f\transformed\jetified-plumber-android-core-2.10\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:plumber-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\b22f2d36704a10d6d0c9b55e100d666f\transformed\jetified-plumber-android-core-2.10\AndroidManifest.xml:5:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e7ca046154a3cdfdc427cab1ab602fbc\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite-framework:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\e7ca046154a3cdfdc427cab1ab602fbc\transformed\sqlite-framework-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [com.squareup.leakcanary:leakcanary-android-utils:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\0d78391c2760c4537eda9077bb4f87b7\transformed\jetified-leakcanary-android-utils-2.10\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.leakcanary:leakcanary-android-utils:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\0d78391c2760c4537eda9077bb4f87b7\transformed\jetified-leakcanary-android-utils-2.10\AndroidManifest.xml:5:5-44
MERGED from [com.squareup.curtains:curtains:1.2.4] C:\Users\<USER>\.gradle\caches\transforms-4\00f99faa29e9b9558ed7a4323dc949b4\transformed\jetified-curtains-1.2.4\AndroidManifest.xml:5:5-7:41
MERGED from [com.squareup.curtains:curtains:1.2.4] C:\Users\<USER>\.gradle\caches\transforms-4\00f99faa29e9b9558ed7a4323dc949b4\transformed\jetified-curtains-1.2.4\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\1b67ab735444fe9da40fb99582ba89ec\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.sqlite:sqlite:2.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\1b67ab735444fe9da40fb99582ba89ec\transformed\sqlite-2.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c886296f8a815b83add004e4ea2b670\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cardview:cardview:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\5c886296f8a815b83add004e4ea2b670\transformed\cardview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\e8b3344ea7319a7021dd2922924f5d55\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\e8b3344ea7319a7021dd2922924f5d55\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\92e7a0203c6e4f0920aa306c34c2c22a\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\92e7a0203c6e4f0920aa306c34c2c22a\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\20793f5baa1d05e9d84b902adf529651\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\20793f5baa1d05e9d84b902adf529651\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:21:5-44
MERGED from [com.github.bumptech.glide:gifdecoder:4.14.2] C:\Users\<USER>\.gradle\caches\transforms-4\d14492c9e44ff7606142ae00f753b205\transformed\jetified-gifdecoder-4.14.2\AndroidManifest.xml:5:5-7:41
MERGED from [com.github.bumptech.glide:gifdecoder:4.14.2] C:\Users\<USER>\.gradle\caches\transforms-4\d14492c9e44ff7606142ae00f753b205\transformed\jetified-gifdecoder-4.14.2\AndroidManifest.xml:5:5-7:41
MERGED from [androidx.exifinterface:exifinterface:1.3.3] C:\Users\<USER>\.gradle\caches\transforms-4\9dacbf323299d6786d635444d15b2ae6\transformed\exifinterface-1.3.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.exifinterface:exifinterface:1.3.3] C:\Users\<USER>\.gradle\caches\transforms-4\9dacbf323299d6786d635444d15b2ae6\transformed\exifinterface-1.3.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\21b516116edda89f30d8dcb441874489\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\21b516116edda89f30d8dcb441874489\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e8217cf4d1379041f0d649c39ee3f1c9\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\e8217cf4d1379041f0d649c39ee3f1c9\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\64e5f73b398dfbd6c1519db09deba5ab\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.documentfile:documentfile:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\64e5f73b398dfbd6c1519db09deba5ab\transformed\documentfile-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7bfced2e1a7b76bf07801170f8f677e7\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.localbroadcastmanager:localbroadcastmanager:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7bfced2e1a7b76bf07801170f8f677e7\transformed\localbroadcastmanager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\05705b555e34d28fd179efd453db9fdb\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.print:print:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\05705b555e34d28fd179efd453db9fdb\transformed\print-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\6a25bde3cfc6c0bc2e5556a7b2e98230\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\6a25bde3cfc6c0bc2e5556a7b2e98230\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\891a814344d0e09ae3597ffe3da67f30\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\891a814344d0e09ae3597ffe3da67f30\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4969a8d10287214ceabd1fb5ee380b8e\transformed\jetified-transport-api-3.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [com.google.android.datatransport:transport-api:3.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\4969a8d10287214ceabd1fb5ee380b8e\transformed\jetified-transport-api-3.0.0\AndroidManifest.xml:18:5-20:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\1bc7564f966159972dc6d67fd09a85b9\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] C:\Users\<USER>\.gradle\caches\transforms-4\1bc7564f966159972dc6d67fd09a85b9\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\af472de109f8654b50e8e056af3f9442\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [com.google.firebase:firebase-encoders-json:18.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\af472de109f8654b50e8e056af3f9442\transformed\jetified-firebase-encoders-json-18.0.0\AndroidManifest.xml:19:5-21:41
MERGED from [:indicatorseekbar] D:\Android\KidsDrawingApp\indicatorseekbar\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [:indicatorseekbar] D:\Android\KidsDrawingApp\indicatorseekbar\build\intermediates\merged_manifest\debug\processDebugManifest\AndroidManifest.xml:5:5-44
MERGED from [com.github.yukuku:ambilwarna:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ff9bd2009e5ad31b6e19abe7b702bae\transformed\jetified-ambilwarna-2.0.1\AndroidManifest.xml:8:5-10:41
MERGED from [com.github.yukuku:ambilwarna:2.0.1] C:\Users\<USER>\.gradle\caches\transforms-4\5ff9bd2009e5ad31b6e19abe7b702bae\transformed\jetified-ambilwarna-2.0.1\AndroidManifest.xml:8:5-10:41
MERGED from [com.intuit.sdp:sdp-android:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\cd5880d74125cf1efbbb9940d7720254\transformed\jetified-sdp-android-1.1.0\AndroidManifest.xml:7:5-9:41
MERGED from [com.intuit.sdp:sdp-android:1.1.0] C:\Users\<USER>\.gradle\caches\transforms-4\cd5880d74125cf1efbbb9940d7720254\transformed\jetified-sdp-android-1.1.0\AndroidManifest.xml:7:5-9:41
	tools:overrideLibrary
		ADDED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:10:9-58
	android:targetSdkVersion
		INJECTED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\Android\KidsDrawingApp\app\src\main\AndroidManifest.xml
uses-permission#android.permission.ACCESS_ADSERVICES_ATTRIBUTION
ADDED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:14:5-88
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:28:5-88
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:28:5-88
	android:name
		ADDED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:14:22-85
uses-permission#android.permission.ACCESS_ADSERVICES_AD_ID
ADDED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:15:5-82
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:27:5-82
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:27:5-82
	android:name
		ADDED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:15:22-79
uses-permission#android.permission.AD_SERVICES_CONFIG
ADDED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:16:5-77
	android:name
		ADDED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:16:22-74
queries
ADDED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:18:5-29:15
MERGED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:21:5-25:15
MERGED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:21:5-25:15
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:35:5-68:15
MERGED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:35:5-68:15
intent#action:name:android.intent.action.VIEW+data:scheme:https
ADDED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:19:9-23:18
data
ADDED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:22:13-44
	android:scheme
		ADDED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:22:19-41
intent#action:name:android.intent.action.VIEW+data:scheme:http
ADDED from [com.google.ads.interactivemedia.v3:interactivemedia:3.36.0] C:\Users\<USER>\.gradle\caches\transforms-4\9a5b7d78b5f7642025fbb789eb61e27a\transformed\jetified-interactivemedia-3.36.0\AndroidManifest.xml:24:9-28:18
uses-feature#android.software.leanback
ADDED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:11:5-13:36
	android:required
		ADDED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:13:9-33
	android:name
		ADDED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:12:9-49
uses-feature#android.hardware.touchscreen
ADDED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:14:5-16:36
	android:required
		ADDED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:16:9-33
	android:name
		ADDED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:15:9-52
package#com.google.android.apps.tv.launcherx
ADDED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:22:9-72
	android:name
		ADDED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:22:18-69
package#com.google.android.tvlauncher
ADDED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:23:9-65
	android:name
		ADDED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:23:18-62
package#com.google.android.tvrecommendations
ADDED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:24:9-72
	android:name
		ADDED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:24:18-69
activity#com.google.android.tv.ads.controls.FallbackImageActivity
ADDED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:28:9-32:20
	android:exported
		ADDED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:30:13-37
	android:theme
		ADDED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:31:13-75
	android:name
		ADDED from [com.google.android.tv:tv-ads:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\f8992bbb99f27b442a756634e186b35a\transformed\jetified-tv-ads-1.0.0\AndroidManifest.xml:29:13-84
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\1c6e13e74ed25f6edfeeec81c2400dab\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:29:9-37:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\ecd49d5b530cba4f5c8f35f681951d1f\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\ecd49d5b530cba4f5c8f35f681951d1f\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\21b516116edda89f30d8dcb441874489\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] C:\Users\<USER>\.gradle\caches\transforms-4\21b516116edda89f30d8dcb441874489\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\1c6e13e74ed25f6edfeeec81c2400dab\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\1c6e13e74ed25f6edfeeec81c2400dab\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\1c6e13e74ed25f6edfeeec81c2400dab\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\1c6e13e74ed25f6edfeeec81c2400dab\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\1c6e13e74ed25f6edfeeec81c2400dab\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\1c6e13e74ed25f6edfeeec81c2400dab\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\1c6e13e74ed25f6edfeeec81c2400dab\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
activity#com.google.android.gms.common.api.GoogleApiActivity
ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8d70832d7f1deb228721870d2bb07788\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:9-173
	android:exported
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8d70832d7f1deb228721870d2bb07788\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:146-170
	android:theme
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8d70832d7f1deb228721870d2bb07788\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:86-145
	android:name
		ADDED from [com.google.android.gms:play-services-base:18.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\8d70832d7f1deb228721870d2bb07788\transformed\jetified-play-services-base-18.5.0\AndroidManifest.xml:5:19-85
uses-permission#android.permission.ACCESS_ADSERVICES_TOPICS
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:29:5-83
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:29:22-80
uses-permission#android.permission.RECEIVE_BOOT_COMPLETED
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:30:5-32:31
REJECTED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:25:5-81
	tools:node
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:32:9-28
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:31:9-65
intent#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+data:scheme:https
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:38:9-44:18
category#android.intent.category.BROWSABLE
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:41:13-74
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:41:23-71
intent#action:name:android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:47:9-49:18
action#android.support.customtabs.action.CustomTabsService
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:48:13-90
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:48:21-87
intent#action:name:android.intent.action.INSERT+data:mimeType:vnd.android.cursor.dir/event
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:52:9-56:18
action#android.intent.action.INSERT
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:53:13-67
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:53:21-64
intent#action:name:android.intent.action.VIEW+data:scheme:sms
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:57:9-61:18
intent#action:name:android.intent.action.DIAL+data:path:tel:
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:62:9-66:18
action#android.intent.action.DIAL
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:63:13-65
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:63:21-62
activity#com.google.android.gms.ads.AdActivity
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:73:9-78:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:76:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:78:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:75:13-122
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:77:13-61
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:74:13-65
provider#com.google.android.gms.ads.MobileAdsInitProvider
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:80:9-85:43
	android:authorities
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:82:13-73
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:83:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:85:13-40
	android:initOrder
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:84:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:81:13-76
service#com.google.android.gms.ads.AdService
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:87:9-91:43
	android:enabled
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:89:13-35
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:90:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:91:13-40
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:88:13-64
activity#com.google.android.gms.ads.OutOfContextTestingActivity
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:93:9-97:43
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:96:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:97:13-40
	android:configChanges
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:95:13-122
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:94:13-82
activity#com.google.android.gms.ads.NotificationHandlerActivity
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:98:9-105:43
	android:excludeFromRecents
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:100:13-46
	android:launchMode
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:102:13-44
	android:exported
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:101:13-37
	tools:ignore
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:105:13-40
	android:theme
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:104:13-72
	android:taskAffinity
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:103:13-36
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:99:13-82
meta-data#com.google.android.gms.ads.flag.OPTIMIZE_AD_LOADING
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:107:9-109:36
	android:value
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:109:13-33
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:108:13-79
meta-data#com.google.android.gms.ads.flag.OPTIMIZE_INITIALIZATION
ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:110:9-112:36
	android:value
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:112:13-33
	android:name
		ADDED from [com.google.android.gms:play-services-ads-api:24.3.0] C:\Users\<USER>\.gradle\caches\transforms-4\6d21ee6e5f4e3984c77c26b8296527b5\transformed\jetified-play-services-ads-api-24.3.0\AndroidManifest.xml:111:13-83
uses-permission#android.permission.WAKE_LOCK
ADDED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-4\80a65e5aed6e155b189f4dcb10fd10f6\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:25:5-68
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:23:5-68
MERGED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:23:5-68
	android:name
		ADDED from [com.google.android.gms:play-services-measurement-sdk-api:20.1.2] C:\Users\<USER>\.gradle\caches\transforms-4\80a65e5aed6e155b189f4dcb10fd10f6\transformed\jetified-play-services-measurement-sdk-api-20.1.2\AndroidManifest.xml:25:22-65
uses-library#android.ext.adservices
ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\bfe68e5eb7521661e8a6468894cca0cd\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:23:9-25:40
	android:required
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\bfe68e5eb7521661e8a6468894cca0cd\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:25:13-37
	android:name
		ADDED from [androidx.privacysandbox.ads:ads-adservices:1.0.0-beta05] C:\Users\<USER>\.gradle\caches\transforms-4\bfe68e5eb7521661e8a6468894cca0cd\transformed\jetified-ads-adservices-1.0.0-beta05\AndroidManifest.xml:24:13-50
uses-permission#android.permission.FOREGROUND_SERVICE
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:26:5-77
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:26:22-74
meta-data#androidx.work.WorkManagerInitializer
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:34:13-36:52
	android:value
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:36:17-49
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:35:17-68
service#androidx.work.impl.background.systemalarm.SystemAlarmService
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:39:9-45:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:42:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:43:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:44:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:45:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:41:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:40:13-88
service#androidx.work.impl.background.systemjob.SystemJobService
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:46:9-52:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:49:13-70
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:50:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:51:13-69
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:52:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:48:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:47:13-84
service#androidx.work.impl.foreground.SystemForegroundService
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:53:9-59:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:56:13-77
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:57:13-37
	tools:ignore
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:58:13-60
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:59:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:55:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:54:13-81
receiver#androidx.work.impl.utils.ForceStopRunnable$BroadcastReceiver
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:61:9-66:35
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:64:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:65:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:66:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:63:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:62:13-88
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryChargingProxy
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:67:9-77:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:70:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:71:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:72:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:69:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:68:13-106
intent-filter#action:name:android.intent.action.ACTION_POWER_CONNECTED+action:name:android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:73:13-76:29
action#android.intent.action.ACTION_POWER_CONNECTED
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:74:17-87
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:74:25-84
action#android.intent.action.ACTION_POWER_DISCONNECTED
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:75:17-90
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:75:25-87
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$BatteryNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:78:9-88:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:81:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:82:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:83:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:80:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:79:13-104
intent-filter#action:name:android.intent.action.BATTERY_LOW+action:name:android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:84:13-87:29
action#android.intent.action.BATTERY_OKAY
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:85:17-77
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:85:25-74
action#android.intent.action.BATTERY_LOW
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:86:17-76
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:86:25-73
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$StorageNotLowProxy
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:89:9-99:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:92:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:93:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:94:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:91:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:90:13-104
intent-filter#action:name:android.intent.action.DEVICE_STORAGE_LOW+action:name:android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:95:13-98:29
action#android.intent.action.DEVICE_STORAGE_LOW
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:96:17-83
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:96:25-80
action#android.intent.action.DEVICE_STORAGE_OK
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:97:17-82
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:97:25-79
receiver#androidx.work.impl.background.systemalarm.ConstraintProxy$NetworkStateProxy
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:100:9-109:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:103:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:104:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:105:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:102:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:101:13-103
intent-filter#action:name:android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:106:13-108:29
action#android.net.conn.CONNECTIVITY_CHANGE
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:107:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:107:25-76
receiver#androidx.work.impl.background.systemalarm.RescheduleReceiver
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:110:9-121:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:113:13-36
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:114:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:115:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:112:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:111:13-88
intent-filter#action:name:android.intent.action.BOOT_COMPLETED+action:name:android.intent.action.TIMEZONE_CHANGED+action:name:android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:116:13-120:29
action#android.intent.action.BOOT_COMPLETED
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:117:17-79
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:117:25-76
action#android.intent.action.TIME_SET
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:118:17-73
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:118:25-70
action#android.intent.action.TIMEZONE_CHANGED
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:119:17-81
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:119:25-78
receiver#androidx.work.impl.background.systemalarm.ConstraintProxyUpdateReceiver
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:122:9-131:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:125:13-72
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:126:13-37
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:127:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:124:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:123:13-99
intent-filter#action:name:androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:128:13-130:29
action#androidx.work.impl.background.systemalarm.UpdateProxies
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:129:17-98
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:129:25-95
receiver#androidx.work.impl.diagnostics.DiagnosticsReceiver
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:132:9-142:20
	android:enabled
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:135:13-35
	android:exported
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:136:13-36
	android:permission
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:137:13-57
	tools:targetApi
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:138:13-32
	android:directBootAware
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:134:13-44
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:133:13-78
intent-filter#action:name:androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:139:13-141:29
action#androidx.work.diagnostics.REQUEST_DIAGNOSTICS
ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:140:17-88
	android:name
		ADDED from [androidx.work:work-runtime:2.9.1] C:\Users\<USER>\.gradle\caches\transforms-4\72c68965a34ff582a5639f94ca51a3a4\transformed\work-runtime-2.9.1\AndroidManifest.xml:140:25-85
uses-library#androidx.window.extensions
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f956628949a3250544a83e00b93b077\transformed\jetified-window-1.0.0\AndroidManifest.xml:25:9-27:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f956628949a3250544a83e00b93b077\transformed\jetified-window-1.0.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f956628949a3250544a83e00b93b077\transformed\jetified-window-1.0.0\AndroidManifest.xml:26:13-54
uses-library#androidx.window.sidecar
ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f956628949a3250544a83e00b93b077\transformed\jetified-window-1.0.0\AndroidManifest.xml:28:9-30:40
	android:required
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f956628949a3250544a83e00b93b077\transformed\jetified-window-1.0.0\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [androidx.window:window:1.0.0] C:\Users\<USER>\.gradle\caches\transforms-4\7f956628949a3250544a83e00b93b077\transformed\jetified-window-1.0.0\AndroidManifest.xml:29:13-51
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\c3a5dd56922f0d9d5fe7ff90c31db109\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\c3a5dd56922f0d9d5fe7ff90c31db109\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\c3a5dd56922f0d9d5fe7ff90c31db109\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
permission#drawing.jaraappskids.kidsdrawing.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\c3a5dd56922f0d9d5fe7ff90c31db109\transformed\core-1.13.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\c3a5dd56922f0d9d5fe7ff90c31db109\transformed\core-1.13.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\c3a5dd56922f0d9d5fe7ff90c31db109\transformed\core-1.13.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\c3a5dd56922f0d9d5fe7ff90c31db109\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\c3a5dd56922f0d9d5fe7ff90c31db109\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
uses-permission#drawing.jaraappskids.kidsdrawing.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\c3a5dd56922f0d9d5fe7ff90c31db109\transformed\core-1.13.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.13.0] C:\Users\<USER>\.gradle\caches\transforms-4\c3a5dd56922f0d9d5fe7ff90c31db109\transformed\core-1.13.0\AndroidManifest.xml:26:22-94
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\ecd49d5b530cba4f5c8f35f681951d1f\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\ecd49d5b530cba4f5c8f35f681951d1f\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.8.7] C:\Users\<USER>\.gradle\caches\transforms-4\ecd49d5b530cba4f5c8f35f681951d1f\transformed\jetified-lifecycle-process-2.8.7\AndroidManifest.xml:30:17-78
uses-permission#android.permission.POST_NOTIFICATIONS
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:29:5-77
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:29:22-74
provider#leakcanary.internal.LeakCanaryFileProvider
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:32:9-40:20
	android:grantUriPermissions
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:36:13-47
	android:authorities
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:34:13-88
	android:exported
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:35:13-37
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:33:13-70
activity#leakcanary.internal.activity.LeakActivity
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:42:9-73:20
	android:label
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:46:13-71
	android:exported
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:44:13-36
	android:icon
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:45:13-52
	android:theme
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:48:13-63
	android:taskAffinity
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:47:13-76
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:43:13-69
intent-filter#action:name:android.intent.action.VIEW+category:name:android.intent.category.BROWSABLE+category:name:android.intent.category.DEFAULT+data:host:*+data:mimeType:*/*+data:pathPattern:.*\\..*\\..*\\..*\\..*\\..*\\..*\\.hprof+data:pathPattern:.*\\..*\\..*\\..*\\..*\\..*\\.hprof+data:pathPattern:.*\\..*\\..*\\..*\\..*\\.hprof+data:pathPattern:.*\\..*\\..*\\..*\\.hprof+data:pathPattern:.*\\..*\\..*\\.hprof+data:pathPattern:.*\\..*\\.hprof+data:pathPattern:.*\\.hprof+data:scheme:content+data:scheme:file
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:49:13-72:29
	android:label
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:49:28-81
category#android.intent.category.DEFAULT
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:52:17-76
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:52:27-73
activity-alias#leakcanary.internal.activity.LeakLauncherActivity
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:75:9-92:26
	android:enabled
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:78:13-66
	android:label
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:81:13-71
	android:exported
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:79:13-36
	android:targetActivity
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:82:13-79
	android:icon
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:80:13-52
	android:banner
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:77:13-59
	android:theme
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:84:13-63
	android:taskAffinity
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:83:13-76
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:76:13-77
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER+category:name:android.intent.category.LEANBACK_LAUNCHER
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:85:13-91:29
category#android.intent.category.LEANBACK_LAUNCHER
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:90:17-86
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:90:27-83
activity#leakcanary.internal.RequestPermissionActivity
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:94:9-100:68
	android:label
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:98:13-82
	android:excludeFromRecents
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:96:13-46
	android:icon
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:97:13-52
	android:theme
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:100:13-65
	android:taskAffinity
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:99:13-76
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:95:13-73
receiver#leakcanary.internal.NotificationReceiver
ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:102:9-77
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-android-core:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\5c5c40945bdb02cf979fa1770a11ffa7\transformed\jetified-leakcanary-android-core-2.10\AndroidManifest.xml:102:19-74
provider#leakcanary.internal.MainProcessAppWatcherInstaller
ADDED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\ae4c90741e6883f9dd637e00a9aec732\transformed\jetified-leakcanary-object-watcher-android-2.10\AndroidManifest.xml:8:9-12:40
	android:enabled
		ADDED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\ae4c90741e6883f9dd637e00a9aec732\transformed\jetified-leakcanary-object-watcher-android-2.10\AndroidManifest.xml:11:13-69
	android:authorities
		ADDED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\ae4c90741e6883f9dd637e00a9aec732\transformed\jetified-leakcanary-object-watcher-android-2.10\AndroidManifest.xml:10:13-72
	android:exported
		ADDED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\ae4c90741e6883f9dd637e00a9aec732\transformed\jetified-leakcanary-object-watcher-android-2.10\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [com.squareup.leakcanary:leakcanary-object-watcher-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\ae4c90741e6883f9dd637e00a9aec732\transformed\jetified-leakcanary-object-watcher-android-2.10\AndroidManifest.xml:9:13-78
provider#leakcanary.internal.PlumberInstaller
ADDED from [com.squareup.leakcanary:plumber-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\979f7e3a02700746ad02a2232efd5b5b\transformed\jetified-plumber-android-2.10\AndroidManifest.xml:8:9-12:40
	android:enabled
		ADDED from [com.squareup.leakcanary:plumber-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\979f7e3a02700746ad02a2232efd5b5b\transformed\jetified-plumber-android-2.10\AndroidManifest.xml:11:13-69
	android:authorities
		ADDED from [com.squareup.leakcanary:plumber-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\979f7e3a02700746ad02a2232efd5b5b\transformed\jetified-plumber-android-2.10\AndroidManifest.xml:10:13-69
	android:exported
		ADDED from [com.squareup.leakcanary:plumber-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\979f7e3a02700746ad02a2232efd5b5b\transformed\jetified-plumber-android-2.10\AndroidManifest.xml:12:13-37
	android:name
		ADDED from [com.squareup.leakcanary:plumber-android:2.10] C:\Users\<USER>\.gradle\caches\transforms-4\979f7e3a02700746ad02a2232efd5b5b\transformed\jetified-plumber-android-2.10\AndroidManifest.xml:9:13-64
service#androidx.room.MultiInstanceInvalidationService
ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\055a1ceb01c3036c4aaf523a4c606cb3\transformed\room-runtime-2.5.0\AndroidManifest.xml:24:9-28:63
	android:exported
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\055a1ceb01c3036c4aaf523a4c606cb3\transformed\room-runtime-2.5.0\AndroidManifest.xml:27:13-37
	tools:ignore
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\055a1ceb01c3036c4aaf523a4c606cb3\transformed\room-runtime-2.5.0\AndroidManifest.xml:28:13-60
	android:directBootAware
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\055a1ceb01c3036c4aaf523a4c606cb3\transformed\room-runtime-2.5.0\AndroidManifest.xml:26:13-43
	android:name
		ADDED from [androidx.room:room-runtime:2.5.0] C:\Users\<USER>\.gradle\caches\transforms-4\055a1ceb01c3036c4aaf523a4c606cb3\transformed\room-runtime-2.5.0\AndroidManifest.xml:25:13-74
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\transforms-4\8b07a205cfb7ad54647429cd4bf3f5c4\transformed\jetified-profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
service#com.google.android.datatransport.runtime.backends.TransportBackendDiscovery
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\6a25bde3cfc6c0bc2e5556a7b2e98230\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:28:9-34:19
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\891a814344d0e09ae3597ffe3da67f30\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:36:9-38:40
MERGED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\891a814344d0e09ae3597ffe3da67f30\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:36:9-38:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\6a25bde3cfc6c0bc2e5556a7b2e98230\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:30:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\6a25bde3cfc6c0bc2e5556a7b2e98230\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:29:13-103
meta-data#backend:com.google.android.datatransport.cct.CctBackendFactory
ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\6a25bde3cfc6c0bc2e5556a7b2e98230\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:31:13-33:39
	android:value
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\6a25bde3cfc6c0bc2e5556a7b2e98230\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:33:17-36
	android:name
		ADDED from [com.google.android.datatransport:transport-backend-cct:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\6a25bde3cfc6c0bc2e5556a7b2e98230\transformed\jetified-transport-backend-cct-3.1.8\AndroidManifest.xml:32:17-94
service#com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\891a814344d0e09ae3597ffe3da67f30\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:26:9-30:19
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\891a814344d0e09ae3597ffe3da67f30\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:28:13-37
	android:permission
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\891a814344d0e09ae3597ffe3da67f30\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:29:13-69
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\891a814344d0e09ae3597ffe3da67f30\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:27:13-117
receiver#com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver
ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\891a814344d0e09ae3597ffe3da67f30\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:32:9-34:40
	android:exported
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\891a814344d0e09ae3597ffe3da67f30\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:34:13-37
	android:name
		ADDED from [com.google.android.datatransport:transport-runtime:3.1.8] C:\Users\<USER>\.gradle\caches\transforms-4\891a814344d0e09ae3597ffe3da67f30\transformed\jetified-transport-runtime-3.1.8\AndroidManifest.xml:33:13-132
