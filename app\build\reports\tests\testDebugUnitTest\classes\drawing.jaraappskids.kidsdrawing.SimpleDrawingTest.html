<!DOCTYPE html>
<html>
<head>
<meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
<meta http-equiv="x-ua-compatible" content="IE=edge"/>
<title>Test results - Class drawing.jaraappskids.kidsdrawing.SimpleDrawingTest</title>
<link href="../css/base-style.css" rel="stylesheet" type="text/css"/>
<link href="../css/style.css" rel="stylesheet" type="text/css"/>
<script src="../js/report.js" type="text/javascript"></script>
</head>
<body>
<div id="content">
<h1>Class drawing.jaraappskids.kidsdrawing.SimpleDrawingTest</h1>
<div class="breadcrumbs">
<a href="../index.html">all</a> &gt; 
<a href="../packages/drawing.jaraappskids.kidsdrawing.html">drawing.jaraappskids.kidsdrawing</a> &gt; SimpleDrawingTest</div>
<div id="summary">
<table>
<tr>
<td>
<div class="summaryGroup">
<table>
<tr>
<td>
<div class="infoBox" id="tests">
<div class="counter">7</div>
<p>tests</p>
</div>
</td>
<td>
<div class="infoBox" id="failures">
<div class="counter">0</div>
<p>failures</p>
</div>
</td>
<td>
<div class="infoBox" id="ignored">
<div class="counter">0</div>
<p>ignored</p>
</div>
</td>
<td>
<div class="infoBox" id="duration">
<div class="counter">2.971s</div>
<p>duration</p>
</div>
</td>
</tr>
</table>
</div>
</td>
<td>
<div class="infoBox success" id="successRate">
<div class="percent">100%</div>
<p>successful</p>
</div>
</td>
</tr>
</table>
</div>
<div id="tabs">
<ul class="tabLinks">
<li>
<a href="#tab0">Tests</a>
</li>
<li>
<a href="#tab1">Standard output</a>
</li>
</ul>
<div id="tab0" class="tab">
<h2>Tests</h2>
<table>
<thead>
<tr>
<th>Test</th>
<th>Duration</th>
<th>Result</th>
</tr>
</thead>
<tr>
<td class="success">test basic color operations</td>
<td class="success">0.029s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">test basic math operations for drawing</td>
<td class="success">0.040s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">test color palette generation</td>
<td class="success">0.026s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">test drawing bounds calculation</td>
<td class="success">0.028s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">test memory usage simulation</td>
<td class="success">0.024s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">test performance measurement simulation</td>
<td class="success">2.757s</td>
<td class="success">passed</td>
</tr>
<tr>
<td class="success">test stroke width validation</td>
<td class="success">0.067s</td>
<td class="success">passed</td>
</tr>
</table>
</div>
<div id="tab1" class="tab">
<h2>Standard output</h2>
<span class="code">
<pre>Called loadFromPath(/system/framework/framework-res.apk, true); mode=binary sdk=28
</pre>
</span>
</div>
</div>
<div id="footer">
<p>
<div>
<label class="hidden" id="label-for-line-wrapping-toggle" for="line-wrapping-toggle">Wrap lines
<input id="line-wrapping-toggle" type="checkbox" autocomplete="off"/>
</label>
</div>Generated by 
<a href="http://www.gradle.org">Gradle 8.7</a> at 19-Jun-2025, 10:22:32 am</p>
</div>
</div>
</body>
</html>
