  Context android.content  Bitmap android.graphics  BlurMaskFilter android.graphics  Boolean android.graphics  Canvas android.graphics  Color android.graphics  DrawingViewState android.graphics  Float android.graphics  
FloatArray android.graphics  Int android.graphics  List android.graphics  Long android.graphics  Math android.graphics  
MemoryInfo android.graphics  Mockito android.graphics  MotionEvent android.graphics  Pair android.graphics  Path android.graphics  Random android.graphics  Runtime android.graphics  System android.graphics  Thread android.graphics  View android.graphics  apply android.graphics  floatArrayOf android.graphics  invoke android.graphics  java android.graphics  listOf android.graphics  
mutableListOf android.graphics  	nextFloat android.graphics  step android.graphics  until android.graphics  Config android.graphics.Bitmap  createBitmap android.graphics.Bitmap  getPixel android.graphics.Bitmap  height android.graphics.Bitmap  
isRecycled android.graphics.Bitmap  recycle android.graphics.Bitmap  width android.graphics.Bitmap  	ARGB_8888 android.graphics.Bitmap.Config  	drawColor android.graphics.Canvas  BLACK android.graphics.Color  BLUE android.graphics.Color  CYAN android.graphics.Color  DKGRAY android.graphics.Color  GRAY android.graphics.Color  GREEN android.graphics.Color  LTGRAY android.graphics.Color  MAGENTA android.graphics.Color  RED android.graphics.Color  TRANSPARENT android.graphics.Color  WHITE android.graphics.Color  YELLOW android.graphics.Color  argb android.graphics.Color  rgb android.graphics.Color  Random android.graphics.Path  apply android.graphics.Path  lineTo android.graphics.Path  moveTo android.graphics.Path  	nextFloat android.graphics.Path  AttributeSet android.util  MotionEvent android.view  View android.view  ACTION_DOWN android.view.MotionEvent  ACTION_MOVE android.view.MotionEvent  	ACTION_UP android.view.MotionEvent  action android.view.MotionEvent  downTime android.view.MotionEvent  	eventTime android.view.MotionEvent  x android.view.MotionEvent  y android.view.MotionEvent  layout android.view.View  	isAtLeast &com.google.common.truth.IntegerSubject  isAtMost &com.google.common.truth.IntegerSubject  
isLessThan #com.google.common.truth.LongSubject  	isNotNull com.google.common.truth.Subject  
assertThat com.google.common.truth.Truth  Boolean  drawing.jaraappskids.kidsdrawing  
BrushToolTest  drawing.jaraappskids.kidsdrawing  EraserToolTest  drawing.jaraappskids.kidsdrawing  ExampleUnitTest  drawing.jaraappskids.kidsdrawing  Int  drawing.jaraappskids.kidsdrawing  KidsDrawingTestSuite  drawing.jaraappskids.kidsdrawing  Long  drawing.jaraappskids.kidsdrawing  RunWith  drawing.jaraappskids.kidsdrawing  Suite  drawing.jaraappskids.kidsdrawing  Test  drawing.jaraappskids.kidsdrawing  TestConfiguration  drawing.jaraappskids.kidsdrawing  UndoRedoSystemTest  drawing.jaraappskids.kidsdrawing  WTDrawingViewTest  drawing.jaraappskids.kidsdrawing  assertEquals  drawing.jaraappskids.kidsdrawing  println  drawing.jaraappskids.kidsdrawing  repeat  drawing.jaraappskids.kidsdrawing  assertEquals 0drawing.jaraappskids.kidsdrawing.ExampleUnitTest  Boolean 5drawing.jaraappskids.kidsdrawing.KidsDrawingTestSuite  Int 5drawing.jaraappskids.kidsdrawing.KidsDrawingTestSuite  Long 5drawing.jaraappskids.kidsdrawing.KidsDrawingTestSuite  TestConfiguration 5drawing.jaraappskids.kidsdrawing.KidsDrawingTestSuite  println 5drawing.jaraappskids.kidsdrawing.KidsDrawingTestSuite  repeat 5drawing.jaraappskids.kidsdrawing.KidsDrawingTestSuite  TestConfiguration ?drawing.jaraappskids.kidsdrawing.KidsDrawingTestSuite.Companion  println ?drawing.jaraappskids.kidsdrawing.KidsDrawingTestSuite.Companion  repeat ?drawing.jaraappskids.kidsdrawing.KidsDrawingTestSuite.Companion  SuiteClasses &drawing.jaraappskids.kidsdrawing.Suite  After %drawing.jaraappskids.kidsdrawing.base  AssertionError %drawing.jaraappskids.kidsdrawing.base  AttributeSet %drawing.jaraappskids.kidsdrawing.base  BaseDrawingTest %drawing.jaraappskids.kidsdrawing.base  Before %drawing.jaraappskids.kidsdrawing.base  Bitmap %drawing.jaraappskids.kidsdrawing.base  Boolean %drawing.jaraappskids.kidsdrawing.base  Color %drawing.jaraappskids.kidsdrawing.base  Config %drawing.jaraappskids.kidsdrawing.base  Context %drawing.jaraappskids.kidsdrawing.base  DrawingView %drawing.jaraappskids.kidsdrawing.base  	Exception %drawing.jaraappskids.kidsdrawing.base  Float %drawing.jaraappskids.kidsdrawing.base  Int %drawing.jaraappskids.kidsdrawing.base  Long %drawing.jaraappskids.kidsdrawing.base  Mockito %drawing.jaraappskids.kidsdrawing.base  MockitoAnnotations %drawing.jaraappskids.kidsdrawing.base  RobolectricTestRunner %drawing.jaraappskids.kidsdrawing.base  RunWith %drawing.jaraappskids.kidsdrawing.base  RuntimeEnvironment %drawing.jaraappskids.kidsdrawing.base  System %drawing.jaraappskids.kidsdrawing.base  	TestUtils %drawing.jaraappskids.kidsdrawing.base  Unit %drawing.jaraappskids.kidsdrawing.base  apply %drawing.jaraappskids.kidsdrawing.base  
assertThat %drawing.jaraappskids.kidsdrawing.base  createTestBitmap %drawing.jaraappskids.kidsdrawing.base  forEach %drawing.jaraappskids.kidsdrawing.base  getTestBrushSizes %drawing.jaraappskids.kidsdrawing.base  getTestColorPalette %drawing.jaraappskids.kidsdrawing.base  java %drawing.jaraappskids.kidsdrawing.base  measureMemoryUsage %drawing.jaraappskids.kidsdrawing.base  
mutableListOf %drawing.jaraappskids.kidsdrawing.base  println %drawing.jaraappskids.kidsdrawing.base  repeat %drawing.jaraappskids.kidsdrawing.base  simulateDrawingStroke %drawing.jaraappskids.kidsdrawing.base  waitForUIThread %drawing.jaraappskids.kidsdrawing.base  AssertionError 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  AttributeSet 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  Color 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  DrawingView 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  Mockito 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  MockitoAnnotations 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  RuntimeEnvironment 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  System 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  	TestUtils 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  apply 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  assertNoExceptions 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  
assertThat 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  checkMemoryLeaks 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  context 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  createComplexDrawingScenario 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  createTestBitmap 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  drawingView 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  getTestBrushSizes 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  getTestColorPalette 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  
initialMemory 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  java 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  measureMemoryUsage 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  measurePerformance 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  
mutableListOf 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  performRapidToolSwitching 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  println 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  repeat 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  setupDrawingView 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  simulateDrawing 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  simulateDrawingStroke 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  testBitmaps 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  waitForDrawingCompletion 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  waitForUIThread 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  
MemoryInfo /drawing.jaraappskids.kidsdrawing.base.TestUtils  DrawingView 'drawing.jaraappskids.kidsdrawing.custom  apply 3drawing.jaraappskids.kidsdrawing.custom.DrawingView  cleanup 3drawing.jaraappskids.kidsdrawing.custom.DrawingView  clearCanvas 3drawing.jaraappskids.kidsdrawing.custom.DrawingView  layout 3drawing.jaraappskids.kidsdrawing.custom.DrawingView  onTouchEvent 3drawing.jaraappskids.kidsdrawing.custom.DrawingView  redo 3drawing.jaraappskids.kidsdrawing.custom.DrawingView  setBackgroundColor 3drawing.jaraappskids.kidsdrawing.custom.DrawingView  
setPaintColor 3drawing.jaraappskids.kidsdrawing.custom.DrawingView  setPaintStrokeWidth 3drawing.jaraappskids.kidsdrawing.custom.DrawingView  undo 3drawing.jaraappskids.kidsdrawing.custom.DrawingView  BaseDrawingTest (drawing.jaraappskids.kidsdrawing.drawing  Color (drawing.jaraappskids.kidsdrawing.drawing  Int (drawing.jaraappskids.kidsdrawing.drawing  RobolectricTestRunner (drawing.jaraappskids.kidsdrawing.drawing  RunWith (drawing.jaraappskids.kidsdrawing.drawing  Test (drawing.jaraappskids.kidsdrawing.drawing  	TestUtils (drawing.jaraappskids.kidsdrawing.drawing  WTDrawingViewTest (drawing.jaraappskids.kidsdrawing.drawing  android (drawing.jaraappskids.kidsdrawing.drawing  
assertThat (drawing.jaraappskids.kidsdrawing.drawing  createComplexDrawingPattern (drawing.jaraappskids.kidsdrawing.drawing  createMockMotionEvent (drawing.jaraappskids.kidsdrawing.drawing  forEach (drawing.jaraappskids.kidsdrawing.drawing  forEachIndexed (drawing.jaraappskids.kidsdrawing.drawing  getTestBrushSizes (drawing.jaraappskids.kidsdrawing.drawing  getTestColorPalette (drawing.jaraappskids.kidsdrawing.drawing  
intArrayOf (drawing.jaraappskids.kidsdrawing.drawing  measureMemoryUsage (drawing.jaraappskids.kidsdrawing.drawing  repeat (drawing.jaraappskids.kidsdrawing.drawing  Color :drawing.jaraappskids.kidsdrawing.drawing.WTDrawingViewTest  Int :drawing.jaraappskids.kidsdrawing.drawing.WTDrawingViewTest  	TestUtils :drawing.jaraappskids.kidsdrawing.drawing.WTDrawingViewTest  android :drawing.jaraappskids.kidsdrawing.drawing.WTDrawingViewTest  assertNoExceptions :drawing.jaraappskids.kidsdrawing.drawing.WTDrawingViewTest  
assertThat :drawing.jaraappskids.kidsdrawing.drawing.WTDrawingViewTest  createComplexDrawingPattern :drawing.jaraappskids.kidsdrawing.drawing.WTDrawingViewTest  createComplexDrawingScenario :drawing.jaraappskids.kidsdrawing.drawing.WTDrawingViewTest  createMockMotionEvent :drawing.jaraappskids.kidsdrawing.drawing.WTDrawingViewTest  drawingView :drawing.jaraappskids.kidsdrawing.drawing.WTDrawingViewTest  forEach :drawing.jaraappskids.kidsdrawing.drawing.WTDrawingViewTest  forEachIndexed :drawing.jaraappskids.kidsdrawing.drawing.WTDrawingViewTest  getTestBrushSizes :drawing.jaraappskids.kidsdrawing.drawing.WTDrawingViewTest  getTestColorPalette :drawing.jaraappskids.kidsdrawing.drawing.WTDrawingViewTest  
intArrayOf :drawing.jaraappskids.kidsdrawing.drawing.WTDrawingViewTest  measureMemoryUsage :drawing.jaraappskids.kidsdrawing.drawing.WTDrawingViewTest  measurePerformance :drawing.jaraappskids.kidsdrawing.drawing.WTDrawingViewTest  performRapidToolSwitching :drawing.jaraappskids.kidsdrawing.drawing.WTDrawingViewTest  repeat :drawing.jaraappskids.kidsdrawing.drawing.WTDrawingViewTest  setupDrawingView :drawing.jaraappskids.kidsdrawing.drawing.WTDrawingViewTest  simulateDrawing :drawing.jaraappskids.kidsdrawing.drawing.WTDrawingViewTest  waitForDrawingCompletion :drawing.jaraappskids.kidsdrawing.drawing.WTDrawingViewTest  BaseDrawingTest )drawing.jaraappskids.kidsdrawing.features  Color )drawing.jaraappskids.kidsdrawing.features  RobolectricTestRunner )drawing.jaraappskids.kidsdrawing.features  RunWith )drawing.jaraappskids.kidsdrawing.features  Test )drawing.jaraappskids.kidsdrawing.features  	TestUtils )drawing.jaraappskids.kidsdrawing.features  Triple )drawing.jaraappskids.kidsdrawing.features  UndoRedoSystemTest )drawing.jaraappskids.kidsdrawing.features  android )drawing.jaraappskids.kidsdrawing.features  
assertThat )drawing.jaraappskids.kidsdrawing.features  createComplexDrawingPattern )drawing.jaraappskids.kidsdrawing.features  createMockMotionEvent )drawing.jaraappskids.kidsdrawing.features  forEachIndexed )drawing.jaraappskids.kidsdrawing.features  listOf )drawing.jaraappskids.kidsdrawing.features  measureMemoryUsage )drawing.jaraappskids.kidsdrawing.features  repeat )drawing.jaraappskids.kidsdrawing.features  take )drawing.jaraappskids.kidsdrawing.features  Color <drawing.jaraappskids.kidsdrawing.features.UndoRedoSystemTest  	TestUtils <drawing.jaraappskids.kidsdrawing.features.UndoRedoSystemTest  Triple <drawing.jaraappskids.kidsdrawing.features.UndoRedoSystemTest  android <drawing.jaraappskids.kidsdrawing.features.UndoRedoSystemTest  assertNoExceptions <drawing.jaraappskids.kidsdrawing.features.UndoRedoSystemTest  
assertThat <drawing.jaraappskids.kidsdrawing.features.UndoRedoSystemTest  createComplexDrawingPattern <drawing.jaraappskids.kidsdrawing.features.UndoRedoSystemTest  createComplexDrawingScenario <drawing.jaraappskids.kidsdrawing.features.UndoRedoSystemTest  createMockMotionEvent <drawing.jaraappskids.kidsdrawing.features.UndoRedoSystemTest  drawingView <drawing.jaraappskids.kidsdrawing.features.UndoRedoSystemTest  forEachIndexed <drawing.jaraappskids.kidsdrawing.features.UndoRedoSystemTest  listOf <drawing.jaraappskids.kidsdrawing.features.UndoRedoSystemTest  measureMemoryUsage <drawing.jaraappskids.kidsdrawing.features.UndoRedoSystemTest  measurePerformance <drawing.jaraappskids.kidsdrawing.features.UndoRedoSystemTest  repeat <drawing.jaraappskids.kidsdrawing.features.UndoRedoSystemTest  setupDrawingView <drawing.jaraappskids.kidsdrawing.features.UndoRedoSystemTest  simulateDrawing <drawing.jaraappskids.kidsdrawing.features.UndoRedoSystemTest  take <drawing.jaraappskids.kidsdrawing.features.UndoRedoSystemTest  waitForDrawingCompletion <drawing.jaraappskids.kidsdrawing.features.UndoRedoSystemTest  BaseDrawingTest &drawing.jaraappskids.kidsdrawing.tools  
BrushToolTest &drawing.jaraappskids.kidsdrawing.tools  Color &drawing.jaraappskids.kidsdrawing.tools  EraserToolTest &drawing.jaraappskids.kidsdrawing.tools  Pair &drawing.jaraappskids.kidsdrawing.tools  RobolectricTestRunner &drawing.jaraappskids.kidsdrawing.tools  RunWith &drawing.jaraappskids.kidsdrawing.tools  Test &drawing.jaraappskids.kidsdrawing.tools  	TestUtils &drawing.jaraappskids.kidsdrawing.tools  android &drawing.jaraappskids.kidsdrawing.tools  
assertThat &drawing.jaraappskids.kidsdrawing.tools  createComplexDrawingPattern &drawing.jaraappskids.kidsdrawing.tools  createMockMotionEvent &drawing.jaraappskids.kidsdrawing.tools  forEach &drawing.jaraappskids.kidsdrawing.tools  forEachIndexed &drawing.jaraappskids.kidsdrawing.tools  
intArrayOf &drawing.jaraappskids.kidsdrawing.tools  listOf &drawing.jaraappskids.kidsdrawing.tools  measureMemoryUsage &drawing.jaraappskids.kidsdrawing.tools  repeat &drawing.jaraappskids.kidsdrawing.tools  take &drawing.jaraappskids.kidsdrawing.tools  Color 4drawing.jaraappskids.kidsdrawing.tools.BrushToolTest  Pair 4drawing.jaraappskids.kidsdrawing.tools.BrushToolTest  	TestUtils 4drawing.jaraappskids.kidsdrawing.tools.BrushToolTest  android 4drawing.jaraappskids.kidsdrawing.tools.BrushToolTest  assertNoExceptions 4drawing.jaraappskids.kidsdrawing.tools.BrushToolTest  
assertThat 4drawing.jaraappskids.kidsdrawing.tools.BrushToolTest  createComplexDrawingPattern 4drawing.jaraappskids.kidsdrawing.tools.BrushToolTest  createMockMotionEvent 4drawing.jaraappskids.kidsdrawing.tools.BrushToolTest  drawingView 4drawing.jaraappskids.kidsdrawing.tools.BrushToolTest  forEach 4drawing.jaraappskids.kidsdrawing.tools.BrushToolTest  forEachIndexed 4drawing.jaraappskids.kidsdrawing.tools.BrushToolTest  
intArrayOf 4drawing.jaraappskids.kidsdrawing.tools.BrushToolTest  listOf 4drawing.jaraappskids.kidsdrawing.tools.BrushToolTest  measureMemoryUsage 4drawing.jaraappskids.kidsdrawing.tools.BrushToolTest  measurePerformance 4drawing.jaraappskids.kidsdrawing.tools.BrushToolTest  repeat 4drawing.jaraappskids.kidsdrawing.tools.BrushToolTest  setupDrawingView 4drawing.jaraappskids.kidsdrawing.tools.BrushToolTest  simulateDrawing 4drawing.jaraappskids.kidsdrawing.tools.BrushToolTest  take 4drawing.jaraappskids.kidsdrawing.tools.BrushToolTest  waitForDrawingCompletion 4drawing.jaraappskids.kidsdrawing.tools.BrushToolTest  Color 5drawing.jaraappskids.kidsdrawing.tools.EraserToolTest  Pair 5drawing.jaraappskids.kidsdrawing.tools.EraserToolTest  	TestUtils 5drawing.jaraappskids.kidsdrawing.tools.EraserToolTest  assertNoExceptions 5drawing.jaraappskids.kidsdrawing.tools.EraserToolTest  
assertThat 5drawing.jaraappskids.kidsdrawing.tools.EraserToolTest  drawingView 5drawing.jaraappskids.kidsdrawing.tools.EraserToolTest  forEachIndexed 5drawing.jaraappskids.kidsdrawing.tools.EraserToolTest  
intArrayOf 5drawing.jaraappskids.kidsdrawing.tools.EraserToolTest  listOf 5drawing.jaraappskids.kidsdrawing.tools.EraserToolTest  measureMemoryUsage 5drawing.jaraappskids.kidsdrawing.tools.EraserToolTest  measurePerformance 5drawing.jaraappskids.kidsdrawing.tools.EraserToolTest  repeat 5drawing.jaraappskids.kidsdrawing.tools.EraserToolTest  setupDrawingView 5drawing.jaraappskids.kidsdrawing.tools.EraserToolTest  simulateDrawing 5drawing.jaraappskids.kidsdrawing.tools.EraserToolTest  waitForDrawingCompletion 5drawing.jaraappskids.kidsdrawing.tools.EraserToolTest  Bitmap &drawing.jaraappskids.kidsdrawing.utils  Boolean &drawing.jaraappskids.kidsdrawing.utils  Canvas &drawing.jaraappskids.kidsdrawing.utils  Color &drawing.jaraappskids.kidsdrawing.utils  DrawingViewState &drawing.jaraappskids.kidsdrawing.utils  Float &drawing.jaraappskids.kidsdrawing.utils  
FloatArray &drawing.jaraappskids.kidsdrawing.utils  Int &drawing.jaraappskids.kidsdrawing.utils  List &drawing.jaraappskids.kidsdrawing.utils  Long &drawing.jaraappskids.kidsdrawing.utils  Math &drawing.jaraappskids.kidsdrawing.utils  
MemoryInfo &drawing.jaraappskids.kidsdrawing.utils  Mockito &drawing.jaraappskids.kidsdrawing.utils  MotionEvent &drawing.jaraappskids.kidsdrawing.utils  Pair &drawing.jaraappskids.kidsdrawing.utils  Path &drawing.jaraappskids.kidsdrawing.utils  Random &drawing.jaraappskids.kidsdrawing.utils  Runtime &drawing.jaraappskids.kidsdrawing.utils  System &drawing.jaraappskids.kidsdrawing.utils  	TestUtils &drawing.jaraappskids.kidsdrawing.utils  Thread &drawing.jaraappskids.kidsdrawing.utils  View &drawing.jaraappskids.kidsdrawing.utils  apply &drawing.jaraappskids.kidsdrawing.utils  floatArrayOf &drawing.jaraappskids.kidsdrawing.utils  invoke &drawing.jaraappskids.kidsdrawing.utils  java &drawing.jaraappskids.kidsdrawing.utils  listOf &drawing.jaraappskids.kidsdrawing.utils  
mutableListOf &drawing.jaraappskids.kidsdrawing.utils  	nextFloat &drawing.jaraappskids.kidsdrawing.utils  step &drawing.jaraappskids.kidsdrawing.utils  until &drawing.jaraappskids.kidsdrawing.utils  Bitmap 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  Boolean 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  Canvas 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  Color 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  DrawingViewState 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  Float 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  
FloatArray 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  Int 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  List 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  Long 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  Math 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  
MemoryInfo 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  Mockito 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  MotionEvent 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  Pair 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  Path 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  Random 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  Runtime 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  System 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  Thread 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  View 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  apply 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  createComplexDrawingPattern 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  createMockMotionEvent 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  createTestBitmap 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  floatArrayOf 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  getTestBrushSizes 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  getTestColorPalette 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  invoke 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  java 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  listOf 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  measureMemoryUsage 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  
mutableListOf 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  	nextFloat 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  simulateDrawingStroke 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  step 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  until 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  waitForUIThread 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  	maxMemory ;drawing.jaraappskids.kidsdrawing.utils.TestUtils.MemoryInfo  
usedMemory ;drawing.jaraappskids.kidsdrawing.utils.TestUtils.MemoryInfo  AssertionError 	java.lang  Class 	java.lang  cos java.lang.Math  sin java.lang.Math  	toRadians java.lang.Math  
freeMemory java.lang.Runtime  
getRuntime java.lang.Runtime  	maxMemory java.lang.Runtime  totalMemory java.lang.Runtime  currentTimeMillis java.lang.System  sleep java.lang.Thread  CharSequence kotlin  	Exception kotlin  
FloatArray kotlin  	Function0 kotlin  	Function1 kotlin  	Function2 kotlin  Int kotlin  IntArray kotlin  Nothing kotlin  Pair kotlin  Triple kotlin  apply kotlin  floatArrayOf kotlin  
intArrayOf kotlin  repeat kotlin  not kotlin.Boolean  toFloat 
kotlin.Double  message kotlin.Exception  	compareTo kotlin.Float  div kotlin.Float  minus kotlin.Float  plus kotlin.Float  rem kotlin.Float  times kotlin.Float  toInt kotlin.Float  forEach kotlin.FloatArray  get kotlin.FloatArray  size kotlin.FloatArray  invoke kotlin.Function0  	Companion 
kotlin.Int  	MAX_VALUE 
kotlin.Int  	MIN_VALUE 
kotlin.Int  div 
kotlin.Int  inc 
kotlin.Int  invoke 
kotlin.Int  minus 
kotlin.Int  plus 
kotlin.Int  rangeTo 
kotlin.Int  rem 
kotlin.Int  times 
kotlin.Int  toDouble 
kotlin.Int  toFloat 
kotlin.Int  toLong 
kotlin.Int  	MAX_VALUE kotlin.Int.Companion  	MIN_VALUE kotlin.Int.Companion  forEach kotlin.IntArray  forEachIndexed kotlin.IntArray  	compareTo kotlin.Long  div kotlin.Long  minus kotlin.Long  plus kotlin.Long  toInt kotlin.Long  
component1 kotlin.Pair  
component2 kotlin.Pair  repeat 
kotlin.String  message kotlin.Throwable  
component1 
kotlin.Triple  
component2 
kotlin.Triple  
component3 
kotlin.Triple  IntIterator kotlin.collections  List kotlin.collections  MutableList kotlin.collections  forEach kotlin.collections  forEachIndexed kotlin.collections  listOf kotlin.collections  
mutableListOf kotlin.collections  take kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  forEachIndexed kotlin.collections.List  get kotlin.collections.List  size kotlin.collections.List  take kotlin.collections.List  add kotlin.collections.MutableList  clear kotlin.collections.MutableList  println 	kotlin.io  java 
kotlin.jvm  Random 
kotlin.random  Default kotlin.random.Random  	nextFloat kotlin.random.Random  	nextFloat kotlin.random.Random.Default  CharProgression 
kotlin.ranges  	CharRange 
kotlin.ranges  IntProgression 
kotlin.ranges  IntRange 
kotlin.ranges  LongProgression 
kotlin.ranges  	LongRange 
kotlin.ranges  UIntProgression 
kotlin.ranges  	UIntRange 
kotlin.ranges  ULongProgression 
kotlin.ranges  
ULongRange 
kotlin.ranges  step 
kotlin.ranges  until 
kotlin.ranges  iterator kotlin.ranges.IntProgression  step kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  step kotlin.ranges.IntRange  java kotlin.reflect.KClass  Sequence kotlin.sequences  forEach kotlin.sequences  forEachIndexed kotlin.sequences  take kotlin.sequences  forEach kotlin.text  forEachIndexed kotlin.text  repeat kotlin.text  take kotlin.text  After 	org.junit  Before 	org.junit  Test 	org.junit  assertEquals 	org.junit  assertEquals org.junit.Assert  RunWith org.junit.runner  Suite org.junit.runners  SuiteClasses org.junit.runners.Suite  Mockito org.mockito  MockitoAnnotations org.mockito  mock org.mockito.Mockito  when org.mockito.Mockito  	openMocks org.mockito.MockitoAnnotations  
thenReturn $org.mockito.stubbing.OngoingStubbing  RobolectricTestRunner org.robolectric  RuntimeEnvironment org.robolectric  getApplication "org.robolectric.RuntimeEnvironment  Config org.robolectric.annotation  	isEqualTo $com.google.common.truth.FloatSubject  
isGreaterThan $com.google.common.truth.FloatSubject  contains 'com.google.common.truth.IterableSubject  containsExactly 'com.google.common.truth.IterableSubject  hasSize 'com.google.common.truth.IterableSubject  
isGreaterThan #com.google.common.truth.LongSubject  	isEqualTo com.google.common.truth.Subject  isNotEqualTo com.google.common.truth.Subject  Color  drawing.jaraappskids.kidsdrawing  Config  drawing.jaraappskids.kidsdrawing  RobolectricTestRunner  drawing.jaraappskids.kidsdrawing  Runtime  drawing.jaraappskids.kidsdrawing  SimpleDrawingTest  drawing.jaraappskids.kidsdrawing  String  drawing.jaraappskids.kidsdrawing  System  drawing.jaraappskids.kidsdrawing  
assertThat  drawing.jaraappskids.kidsdrawing  kotlin  drawing.jaraappskids.kidsdrawing  listOf  drawing.jaraappskids.kidsdrawing  map  drawing.jaraappskids.kidsdrawing  
mutableListOf  drawing.jaraappskids.kidsdrawing  plus  drawing.jaraappskids.kidsdrawing  Color 2drawing.jaraappskids.kidsdrawing.SimpleDrawingTest  Runtime 2drawing.jaraappskids.kidsdrawing.SimpleDrawingTest  System 2drawing.jaraappskids.kidsdrawing.SimpleDrawingTest  
assertThat 2drawing.jaraappskids.kidsdrawing.SimpleDrawingTest  kotlin 2drawing.jaraappskids.kidsdrawing.SimpleDrawingTest  listOf 2drawing.jaraappskids.kidsdrawing.SimpleDrawingTest  map 2drawing.jaraappskids.kidsdrawing.SimpleDrawingTest  
mutableListOf 2drawing.jaraappskids.kidsdrawing.SimpleDrawingTest  plus 2drawing.jaraappskids.kidsdrawing.SimpleDrawingTest  repeat 2drawing.jaraappskids.kidsdrawing.SimpleDrawingTest  
BigDecimal 	java.math  
BigInteger 	java.math  Array kotlin  BooleanArray kotlin  	ByteArray kotlin  	CharArray kotlin  DoubleArray kotlin  	LongArray kotlin  Result kotlin  
ShortArray kotlin  
UByteArray kotlin  	UIntArray kotlin  
ULongArray kotlin  UShortArray kotlin  map kotlin  plus kotlin  Map kotlin.collections  Set kotlin.collections  map kotlin.collections  plus kotlin.collections  map kotlin.collections.List  plus kotlin.collections.List  kotlin 
kotlin.jvm  sqrt kotlin.math  contains kotlin.ranges.IntRange  KClass kotlin.reflect  map kotlin.sequences  plus kotlin.sequences  map kotlin.text  plus kotlin.text                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                           