package drawing.jaraappskids.kidsdrawing.tools

import android.graphics.Color
import com.google.common.truth.Truth.assertThat
import drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest
import drawing.jaraappskids.kidsdrawing.utils.TestUtils
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner

/**
 * Comprehensive tests for Eraser Tool functionality
 * Tests eraser mode toggle, different eraser sizes, and proper path clearing
 */
@RunWith(RobolectricTestRunner::class)
class EraserToolTest : BaseDrawingTest() {

    @Test
    fun `test eraser mode toggle functionality`() {
        // Given: Drawing view in normal mode
        assertThat(drawingView.isDrawingMode()).isFalse()
        
        // When: Enabling eraser mode
        drawingView.setEraserMode(true)
        
        // Then: Should be in eraser mode
        assertThat(drawingView.isDrawingMode()).isTrue()
        
        // When: Disabling eraser mode
        drawingView.setEraserMode(false)
        
        // Then: Should return to normal drawing mode
        assertThat(drawingView.isDrawingMode()).isFalse()
    }

    @Test
    fun `test eraser size adjustment`() {
        // Given: Various eraser sizes
        val eraserSizes = floatArrayOf(5f, 10f, 15f, 20f, 25f, 30f, 35f, 40f)
        
        eraserSizes.forEach { size ->
            // When: Setting eraser width
            drawingView.eraserWidth = size
            
            // Then: Eraser width should be set correctly
            assertThat(drawingView.eraserWidth).isEqualTo(size)
        }
    }

    @Test
    fun `test eraser mode state persistence`() {
        // Given: Eraser mode enabled with specific size
        val eraserSize = 25f
        drawingView.setEraserMode(true)
        drawingView.eraserWidth = eraserSize
        
        // When: Performing eraser operations
        simulateDrawing(20f, 20f, 60f, 60f)
        waitForDrawingCompletion()
        
        // Then: Eraser mode and size should persist
        assertThat(drawingView.isDrawingMode()).isTrue()
        assertThat(drawingView.eraserWidth).isEqualTo(eraserSize)
    }

    @Test
    fun `test eraser vs brush mode switching`() {
        // Given: Initial brush settings
        val brushColor = Color.BLUE
        val brushWidth = 15f
        
        setupDrawingView(brushColor, brushWidth, false)
        
        // When: Switching to eraser mode
        drawingView.setEraserMode(true)
        
        // Then: Should be in eraser mode but brush settings preserved
        assertThat(drawingView.isDrawingMode()).isTrue()
        assertThat(drawingView.getStrokeColor()).isEqualTo(brushColor)
        assertThat(drawingView.strokeWidth).isEqualTo(brushWidth)
        
        // When: Switching back to brush mode
        drawingView.setEraserMode(false)
        
        // Then: Should return to brush mode with original settings
        assertThat(drawingView.isDrawingMode()).isFalse()
        assertThat(drawingView.getStrokeColor()).isEqualTo(brushColor)
        assertThat(drawingView.strokeWidth).isEqualTo(brushWidth)
    }

    @Test
    fun `test eraser functionality after drawing operations`() {
        // Given: Canvas with existing drawing
        setupDrawingView(Color.RED, 12f, false)
        simulateDrawing(10f, 10f, 50f, 50f)
        simulateDrawing(30f, 30f, 70f, 70f)
        waitForDrawingCompletion()
        
        // When: Switching to eraser and erasing
        drawingView.setEraserMode(true)
        drawingView.eraserWidth = 20f
        simulateDrawing(25f, 25f, 55f, 55f) // Erase through the drawing
        
        // Then: Eraser operation should complete without errors
        assertThat(drawingView.isDrawingMode()).isTrue()
        assertThat(drawingView.eraserWidth).isEqualTo(20f)
    }

    @Test
    fun `test multiple eraser sizes in sequence`() {
        // Given: Canvas with drawing to erase
        setupDrawingView(Color.GREEN, 10f, false)
        createComplexDrawingScenario()
        
        // When: Using different eraser sizes
        val eraserSizes = floatArrayOf(10f, 20f, 30f, 15f, 25f)
        
        eraserSizes.forEachIndexed { index, size ->
            drawingView.setEraserMode(true)
            drawingView.eraserWidth = size
            
            val startX = (index * 15f) % 100f
            val startY = (index * 20f) % 100f
            simulateDrawing(startX, startY, startX + 25f, startY + 25f)
            
            // Then: Each eraser size should be applied correctly
            assertThat(drawingView.eraserWidth).isEqualTo(size)
            assertThat(drawingView.isDrawingMode()).isTrue()
        }
    }

    @Test
    fun `test eraser performance with rapid operations`() {
        // Given: Canvas with content to erase
        createComplexDrawingScenario()
        
        // When: Performing rapid eraser operations
        drawingView.setEraserMode(true)
        drawingView.eraserWidth = 18f
        
        val executionTime = measurePerformance {
            repeat(20) { i ->
                val startX = (i * 8f) % 120f
                val startY = (i * 10f) % 120f
                simulateDrawing(startX, startY, startX + 15f, startY + 15f, 3)
            }
        }
        
        // Then: Performance should be acceptable (under 1 second for 20 operations)
        assertThat(executionTime).isLessThan(1000L)
    }

    @Test
    fun `test eraser boundary conditions`() {
        // Test minimum eraser size
        assertNoExceptions {
            drawingView.eraserWidth = 1f
        }
        
        // Test large eraser size
        assertNoExceptions {
            drawingView.eraserWidth = 100f
        }
        
        // Test zero eraser size (should be handled gracefully)
        assertNoExceptions {
            drawingView.eraserWidth = 0f
        }
        
        // Test negative eraser size (should be handled gracefully)
        assertNoExceptions {
            drawingView.eraserWidth = -5f
        }
    }

    @Test
    fun `test eraser with undo and redo operations`() {
        // Given: Canvas with drawing
        setupDrawingView(Color.MAGENTA, 12f, false)
        simulateDrawing(15f, 15f, 45f, 45f)
        
        // When: Erasing and then undoing
        drawingView.setEraserMode(true)
        drawingView.eraserWidth = 22f
        simulateDrawing(30f, 30f, 60f, 60f)
        
        // Perform undo
        drawingView.undo()
        
        // Then: Undo should work with eraser operations
        assertNoExceptions {
            drawingView.redo()
        }
    }

    @Test
    fun `test eraser mode consistency during tool switching`() {
        // Given: Multiple tool mode switches
        val testSequence = listOf(
            Triple(false, Color.RED, 10f),    // Brush mode
            Triple(true, Color.RED, 15f),     // Eraser mode
            Triple(false, Color.BLUE, 20f),   // Back to brush
            Triple(true, Color.BLUE, 25f),    // Eraser again
            Triple(false, Color.GREEN, 12f)   // Final brush mode
        )
        
        testSequence.forEach { (isEraser, color, width) ->
            // When: Switching tool modes
            drawingView.setEraserMode(isEraser)
            drawingView.setStrokeColor(color)
            if (isEraser) {
                drawingView.eraserWidth = width
            } else {
                drawingView.strokeWidth = width
            }
            
            // Then: Mode should be set correctly
            assertThat(drawingView.isDrawingMode()).isEqualTo(isEraser)
            assertThat(drawingView.getStrokeColor()).isEqualTo(color)
        }
    }

    @Test
    fun `test eraser memory usage during extended operations`() {
        // Given: Initial memory state and canvas with content
        val initialMemory = TestUtils.measureMemoryUsage()
        createComplexDrawingScenario()
        
        // When: Performing extended eraser operations
        drawingView.setEraserMode(true)
        
        repeat(30) { i ->
            drawingView.eraserWidth = 10f + (i % 20)
            simulateDrawing(
                (i * 4f) % 100f,
                (i * 6f) % 100f,
                ((i + 8) * 4f) % 100f,
                ((i + 8) * 6f) % 100f
            )
        }
        
        // Then: Memory usage should remain reasonable
        val finalMemory = TestUtils.measureMemoryUsage()
        val memoryIncrease = finalMemory.usedMemory - initialMemory.usedMemory
        val maxAllowedIncrease = 15 * 1024 * 1024 // 15MB
        
        assertThat(memoryIncrease).isLessThan(maxAllowedIncrease.toLong())
    }

    @Test
    fun `test eraser clear functionality`() {
        // Given: Canvas with multiple drawing layers
        setupDrawingView(Color.CYAN, 8f, false)
        simulateDrawing(5f, 5f, 95f, 95f)
        
        setupDrawingView(Color.YELLOW, 15f, false)
        simulateDrawing(20f, 80f, 80f, 20f)
        
        // When: Using eraser and then clearing canvas
        drawingView.setEraserMode(true)
        drawingView.eraserWidth = 30f
        simulateDrawing(40f, 40f, 60f, 60f)
        
        // Clear the entire canvas
        drawingView.clear()
        
        // Then: Clear operation should work regardless of eraser mode
        assertThat(drawingView.isDrawingMode()).isTrue() // Still in eraser mode
        assertNoExceptions {
            // Should be able to continue erasing after clear
            simulateDrawing(10f, 10f, 30f, 30f)
        }
    }
}
