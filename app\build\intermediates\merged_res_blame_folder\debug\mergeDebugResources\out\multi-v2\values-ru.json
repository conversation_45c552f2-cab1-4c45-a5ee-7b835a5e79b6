{"logs": [{"outputFile": "drawing.jaraappskids.kidsdrawing.app-mergeDebugResources-64:/values-ru/values-ru.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\56b21b1e2df5e7a9e2bbd796d8a365f1\\transformed\\material-1.12.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,381,459,537,621,719,810,907,1044,1136,1211,1277,1376,1453,1516,1634,1695,1760,1817,1887,1948,2002,2118,2175,2237,2291,2365,2493,2581,2668,2771,2863,2949,3086,3170,3255,3389,3480,3556,3610,3661,3727,3799,3877,3948,4030,4110,4186,4263,4340,4447,4536,4609,4699,4794,4868,4949,5042,5097,5178,5244,5330,5415,5477,5541,5604,5676,5774,5873,5968,6060,6118,6173,6253,6347,6423", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82", "endColumns": "12,77,77,83,97,90,96,136,91,74,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,86,102,91,85,136,83,84,133,90,75,53,50,65,71,77,70,81,79,75,76,76,106,88,72,89,94,73,80,92,54,80,65,85,84,61,63,62,71,97,98,94,91,57,54,79,93,75,78", "endOffsets": "376,454,532,616,714,805,902,1039,1131,1206,1272,1371,1448,1511,1629,1690,1755,1812,1882,1943,1997,2113,2170,2232,2286,2360,2488,2576,2663,2766,2858,2944,3081,3165,3250,3384,3475,3551,3605,3656,3722,3794,3872,3943,4025,4105,4181,4258,4335,4442,4531,4604,4694,4789,4863,4944,5037,5092,5173,5239,5325,5410,5472,5536,5599,5671,5769,5868,5963,6055,6113,6168,6248,6342,6418,6497"}, "to": {"startLines": "2,36,37,38,39,40,48,49,50,71,72,76,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,156,166,167,168", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3201,3279,3357,3441,3539,4357,4454,4591,7138,7213,7556,8058,8135,8198,8316,8377,8442,8499,8569,8630,8684,8800,8857,8919,8973,9047,9175,9263,9350,9453,9545,9631,9768,9852,9937,10071,10162,10238,10292,10343,10409,10481,10559,10630,10712,10792,10868,10945,11022,11129,11218,11291,11381,11476,11550,11631,11724,11779,11860,11926,12012,12097,12159,12223,12286,12358,12456,12555,12650,12742,12800,14215,14929,15023,15099", "endLines": "7,36,37,38,39,40,48,49,50,71,72,76,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,156,166,167,168", "endColumns": "12,77,77,83,97,90,96,136,91,74,65,98,76,62,117,60,64,56,69,60,53,115,56,61,53,73,127,87,86,102,91,85,136,83,84,133,90,75,53,50,65,71,77,70,81,79,75,76,76,106,88,72,89,94,73,80,92,54,80,65,85,84,61,63,62,71,97,98,94,91,57,54,79,93,75,78", "endOffsets": "426,3274,3352,3436,3534,3625,4449,4586,4678,7208,7274,7650,8130,8193,8311,8372,8437,8494,8564,8625,8679,8795,8852,8914,8968,9042,9170,9258,9345,9448,9540,9626,9763,9847,9932,10066,10157,10233,10287,10338,10404,10476,10554,10625,10707,10787,10863,10940,11017,11124,11213,11286,11376,11471,11545,11626,11719,11774,11855,11921,12007,12092,12154,12218,12281,12353,12451,12550,12645,12737,12795,12850,14290,15018,15094,15173"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8d70832d7f1deb228721870d2bb07788\\transformed\\jetified-play-services-base-18.5.0\\res\\values-ru\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,458,580,686,824,949,1060,1160,1337,1440,1599,1721,1884,2038,2103,2159", "endColumns": "102,161,121,105,137,124,110,99,176,102,158,121,162,153,64,55,81", "endOffsets": "295,457,579,685,823,948,1059,1159,1336,1439,1598,1720,1883,2037,2102,2158,2240"}, "to": {"startLines": "51,52,53,54,55,56,57,58,60,61,62,63,64,65,66,67,68", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4683,4790,4956,5082,5192,5334,5463,5578,5839,6020,6127,6290,6416,6583,6741,6810,6870", "endColumns": "106,165,125,109,141,128,114,103,180,106,162,125,166,157,68,59,85", "endOffsets": "4785,4951,5077,5187,5329,5458,5573,5677,6015,6122,6285,6411,6578,6736,6805,6865,6951"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\ac185097d874e47ec4cafd044e6cc8ed\\transformed\\browser-1.8.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,163,268,380", "endColumns": "107,104,111,104", "endOffsets": "158,263,375,480"}, "to": {"startLines": "70,78,79,80", "startColumns": "4,4,4,4", "startOffsets": "7030,7736,7841,7953", "endColumns": "107,104,111,104", "endOffsets": "7133,7836,7948,8053"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c3a5dd56922f0d9d5fe7ff90c31db109\\transformed\\core-1.13.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,356,457,562,665,782", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "148,250,351,452,557,660,777,878"}, "to": {"startLines": "41,42,43,44,45,46,47,169", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3630,3728,3830,3931,4032,4137,4240,15178", "endColumns": "97,101,100,100,104,102,116,100", "endOffsets": "3723,3825,3926,4027,4132,4235,4352,15274"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c4e3970f58cff9116df172ae1e89ec5d\\transformed\\preference-1.2.1\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,179,271,348,493,662,744", "endColumns": "73,91,76,144,168,81,77", "endOffsets": "174,266,343,488,657,739,817"}, "to": {"startLines": "69,75,144,157,170,171,172", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6956,7464,13022,14295,15279,15448,15530", "endColumns": "73,91,76,144,168,81,77", "endOffsets": "7025,7551,13094,14435,15443,15525,15603"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\6a58776e9dcdced13d931fd31f07cadf\\transformed\\jetified-play-services-ads-24.3.0\\res\\values-ru\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,241,291,354,421,488,588,652,814,952,1084,1134,1194,1338,1426,1475,1556,1593,1630,1677,1758,1805", "endColumns": "41,49,62,66,66,99,63,161,137,131,49,59,143,87,48,80,36,36,46,80,46,55", "endOffsets": "240,290,353,420,487,587,651,813,951,1083,1133,1193,1337,1425,1474,1555,1592,1629,1676,1757,1804,1860"}, "to": {"startLines": "141,142,143,145,146,147,148,149,150,151,152,153,154,155,158,159,160,161,162,163,164,173", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12855,12901,12955,13099,13170,13241,13345,13413,13579,13721,13857,13911,13975,14123,14440,14493,14578,14619,14660,14711,14796,15608", "endColumns": "45,53,66,70,70,103,67,165,141,135,53,63,147,91,52,84,40,40,50,84,50,59", "endOffsets": "12896,12950,13017,13165,13236,13340,13408,13574,13716,13852,13906,13970,14118,14210,14488,14573,14614,14655,14706,14791,14842,15663"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f8992bbb99f27b442a756634e186b35a\\transformed\\jetified-tv-ads-1.0.0\\res\\values-ru\\values.xml", "from": {"startLines": "4,5,6,9", "startColumns": "0,0,0,0", "startOffsets": "197,246,373,590", "endColumns": "48,126,49,76", "endOffsets": "245,372,422,666"}, "to": {"startLines": "35,73,74,77", "startColumns": "4,4,4,4", "startOffsets": "3148,7279,7410,7655", "endColumns": "52,130,53,80", "endOffsets": "3196,7405,7459,7731"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\4886f4714567b788bedb48de3c472517\\transformed\\appcompat-1.7.0\\res\\values-ru\\values-ru.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,220,322,421,507,612,733,812,888,980,1074,1169,1262,1357,1451,1547,1642,1734,1826,1915,2021,2128,2226,2335,2442,2556,2722,2822", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "215,317,416,502,607,728,807,883,975,1069,1164,1257,1352,1446,1542,1637,1729,1821,1910,2016,2123,2221,2330,2437,2551,2717,2817,2899"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,165", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "431,546,648,747,833,938,1059,1138,1214,1306,1400,1495,1588,1683,1777,1873,1968,2060,2152,2241,2347,2454,2552,2661,2768,2882,3048,14847", "endColumns": "114,101,98,85,104,120,78,75,91,93,94,92,94,93,95,94,91,91,88,105,106,97,108,106,113,165,99,81", "endOffsets": "541,643,742,828,933,1054,1133,1209,1301,1395,1490,1583,1678,1772,1868,1963,2055,2147,2236,2342,2449,2547,2656,2763,2877,3043,3143,14924"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\acab93b1b19a0a7fc9f8e2a900142b63\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-ru\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "152", "endOffsets": "347"}, "to": {"startLines": "59", "startColumns": "4", "startOffsets": "5682", "endColumns": "156", "endOffsets": "5834"}}]}]}