package drawing.jaraappskids.kidsdrawing.activities

import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.annotation.SuppressLint
import android.app.ProgressDialog
import android.content.Context
import android.content.Intent
import android.os.AsyncTask
import android.os.Bundle
import android.util.Log
import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.animation.Animation
import android.view.animation.AnimationUtils
import android.widget.TextView
import androidx.appcompat.app.AppCompatActivity
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView
import drawing.jaraappskids.kidsdrawing.R
import drawing.jaraappskids.kidsdrawing.adapters.BGImageAdapter
import drawing.jaraappskids.kidsdrawing.common.CommonConstants
import drawing.jaraappskids.kidsdrawing.common.CommonUtilities
import drawing.jaraappskids.kidsdrawing.custom.CustomProgressDialog
import drawing.jaraappskids.kidsdrawing.databinding.ActivityBgImageListBinding
import drawing.jaraappskids.kidsdrawing.interfaces.AdapterItemCallback
import drawing.jaraappskids.kidsdrawing.interfaces.AdsCallback
import drawing.jaraappskids.kidsdrawing.pojo.BGImageClass
import drawing.jaraappskids.kidsdrawing.utils.Utils
import java.util.*

class BGImageListActivity : AppCompatActivity(), AdapterItemCallback, View.OnClickListener,
    AdsCallback {


    private var levelClassArrayList = ArrayList<BGImageClass>()
    private var isEasy = true

    private lateinit var binding: ActivityBgImageListBinding
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityBgImageListBinding.inflate(layoutInflater)
        setContentView(binding.root)
//        setContentView(R.layout.activity_bg_image_list)
//        window.setFlags(WindowManager.LayoutParams.FLAG_FULLSCREEN, WindowManager.LayoutParams.FLAG_FULLSCREEN)
        initViews()
        loadDataFromIntent()

        // Smart ad loading with internet check
        loadAdsIfOnline()
    }

    override fun onResume() {
        super.onResume()
        // Preload ads using modern ads system
        drawing.jaraappskids.kidsdrawing.ads.ModernAdsManager.preloadInterstitialAd()
    }


    private fun initViews() {
        levelClassArrayList = ArrayList()
        binding.rvBGImageList.layoutManager = GridLayoutManager(
            this,
            2,
            RecyclerView.VERTICAL,
            false
        )
        binding.ivBack.setOnClickListener(this)
        
        // Start kid-friendly animations
        startKidFriendlyAnimations()
    }

    /**
     * Fun animations to make the UI come alive for kids
     */
    private fun startKidFriendlyAnimations() {
        // Animate floating clouds
        animateFloatingClouds()
        
        // Animate sparkles
        animateSparkles()
        
        // Animate title with bounce effect
        animateTitleBounce()
        
        // Animate back button with pulse effect
        animateBackButton()
    }

    private fun animateFloatingClouds() {
        // Find cloud views
        val cloudLeft = findViewById<TextView>(R.id.cloudLeft)
        val cloudRight = findViewById<TextView>(R.id.cloudRight)
        
        cloudLeft?.let { cloud ->
            val animator = ObjectAnimator.ofFloat(cloud, "translationY", 0f, -15f, 0f)
            animator.duration = 3000
            animator.repeatCount = ValueAnimator.INFINITE
            animator.interpolator = AccelerateDecelerateInterpolator()
            animator.start()
        }
        
        cloudRight?.let { cloud ->
            val animator = ObjectAnimator.ofFloat(cloud, "translationY", 0f, 20f, 0f)
            animator.duration = 4000
            animator.repeatCount = ValueAnimator.INFINITE
            animator.interpolator = AccelerateDecelerateInterpolator()
            animator.start()
        }
    }

    private fun animateSparkles() {
        val sparkle1 = findViewById<TextView>(R.id.sparkle1)
        val sparkle2 = findViewById<TextView>(R.id.sparkle2)
        
        sparkle1?.let { sparkle ->
            val scaleAnimator = ObjectAnimator.ofFloat(sparkle, "scaleX", 1f, 1.3f, 1f)
            scaleAnimator.duration = 2000
            scaleAnimator.repeatCount = ValueAnimator.INFINITE
            scaleAnimator.interpolator = AccelerateDecelerateInterpolator()
            scaleAnimator.start()
            
            val scaleYAnimator = ObjectAnimator.ofFloat(sparkle, "scaleY", 1f, 1.3f, 1f)
            scaleYAnimator.duration = 2000
            scaleYAnimator.repeatCount = ValueAnimator.INFINITE
            scaleYAnimator.interpolator = AccelerateDecelerateInterpolator()
            scaleYAnimator.start()
        }
        
        sparkle2?.let { sparkle ->
            val rotationAnimator = ObjectAnimator.ofFloat(sparkle, "rotation", 0f, 360f)
            rotationAnimator.duration = 5000
            rotationAnimator.repeatCount = ValueAnimator.INFINITE
            rotationAnimator.interpolator = AccelerateDecelerateInterpolator()
            rotationAnimator.start()
        }
    }

    private fun animateTitleBounce() {
        binding.tvTitle.let { title ->
            val bounceAnimator = ObjectAnimator.ofFloat(title, "scaleX", 1f, 1.05f, 1f)
            bounceAnimator.duration = 2500
            bounceAnimator.repeatCount = ValueAnimator.INFINITE
            bounceAnimator.interpolator = AccelerateDecelerateInterpolator()
            bounceAnimator.start()
            
            val bounceYAnimator = ObjectAnimator.ofFloat(title, "scaleY", 1f, 1.05f, 1f)
            bounceYAnimator.duration = 2500
            bounceYAnimator.repeatCount = ValueAnimator.INFINITE
            bounceYAnimator.interpolator = AccelerateDecelerateInterpolator()
            bounceYAnimator.start()
        }
    }

    private fun animateBackButton() {
        binding.ivBack.let { backButton ->
            val pulseAnimator = ObjectAnimator.ofFloat(backButton, "alpha", 1f, 0.7f, 1f)
            pulseAnimator.duration = 1500
            pulseAnimator.repeatCount = ValueAnimator.INFINITE
            pulseAnimator.interpolator = AccelerateDecelerateInterpolator()
            pulseAnimator.start()
        }
    }

    private fun loadDataFromIntent() {
        val intent = intent
        if (intent != null && intent.hasExtra(CommonConstants.KeyBGImageType)) {
            isEasy = intent.getBooleanExtra(CommonConstants.KeyBGImageType, true)
            if (isEasy) {
                binding.tvTitle.text = "🌟 Easy Peasy! 🌟"
            } else {
                binding.tvTitle.text = "🚀 Super Challenge! 🚀"
            }
            LoadImageListAsync(this).execute()
        }
    }

    override fun onClick(v: View?) {
        val id = v!!.id
        if (id == R.id.ivBack) {
            finish()
        }
    }

    @SuppressLint("StaticFieldLeak")
    inner class LoadImageListAsync(private val context: Context) : AsyncTask<Void, Void, Void>() {

        lateinit var pDialog: ProgressDialog

        override fun onPreExecute() {
            super.onPreExecute()
            pDialog = CustomProgressDialog.showProgress(context)
        }

        override fun doInBackground(vararg params: Void?): Void? {
            if (isEasy) {
                levelClassArrayList = CommonUtilities.getSmallEasyBGImageList(context)
            } else {
                levelClassArrayList = CommonUtilities.getSmallHardBGImageList(context)
            }
            return null
        }

        override fun onPostExecute(result: Void?) {
            CustomProgressDialog.hideProgress(pDialog)
            binding.rvBGImageList.adapter = BGImageAdapter(context, levelClassArrayList, this@BGImageListActivity)
        }
    }

    var position = 0
    override fun onAdapterItemClick(mPos: Int) {

        position = mPos
        // Always try to show ad first (frequency manager will control if it should show)
        checkAd()

    }

    private fun checkAd() {
        if (Utils.getPref(this, CommonConstants.STATUS_ENABLE_DISABLE, "").equals(CommonConstants.ENABLE)) {
            // Use modern ads manager with smart frequency control
            drawing.jaraappskids.kidsdrawing.ads.ModernAdsManager
                .showInterstitialAd(this, this)
            Utils.setPref(this, CommonConstants.CLICK_IMAGE_COUNT, 1)
        } else {
            startEditorActivity()
        }
    }

    private fun startEditorActivity(){
        CommonConstants.IS_CLEAR = true
        CommonConstants.IS_CLEAR_BRUSH = true
        CommonConstants.IS_CLEAR_MAGIC = true
        CommonConstants.IS_CLEAR_PENCIL = true
        CommonConstants.IS_CLEAR_STICKER = true
        CommonConstants.IS_CLEAR_TEXT = true
        CommonConstants.STICKER_COUNT = 0


        val intent = Intent(this, EditorActivity::class.java)
        intent.putExtra(CommonConstants.KeyBGImageType, isEasy)
        intent.putExtra(CommonConstants.KeyItemPos, position)
        startActivity(intent)
    }

    override fun adLoadingFailed() {
        startEditorActivity()
    }

    override fun adClose() {
        startEditorActivity()
    }

    override fun startNextScreen() {
        startEditorActivity()
    }

    override fun onLoaded() {

    }

    /**
     * Smart ad loading with internet connectivity check
     * Only loads ads if internet is available for better user experience
     */
    private fun loadAdsIfOnline() {
        if (drawing.jaraappskids.kidsdrawing.utils.Utils.isNetworkConnected(this)) {
            // Load banner ad and test method only if internet is available
            try {
                drawing.jaraappskids.kidsdrawing.utils.TestAdsObject.testMethod(this)
                Log.d("BGImageListActivity", "Banner ad loaded successfully with internet connection")
            } catch (e: Exception) {
                Log.e("BGImageListActivity", "Error loading banner ad: ${e.message}")
            }
        } else {
            // No internet - skip ad loading
            Log.d("BGImageListActivity", "No internet connection - skipping banner ad loading")
            // Optional: Show offline mode indication
            showOfflineModeToast()
        }
    }

    /**
     * Show offline mode indication to user (optional)
     */
    private fun showOfflineModeToast() {
        // Uncomment if you want to inform users about offline mode
        // Toast.makeText(this, "Using offline mode", Toast.LENGTH_SHORT).show()
    }

}