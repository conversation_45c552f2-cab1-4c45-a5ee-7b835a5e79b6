  Context android.content  Bitmap android.graphics  BlurMaskFilter android.graphics  Color android.graphics  Pair android.graphics  WTBezierPath android.graphics  MotionEvent android.view  View android.view  Truth com.google.common.truth  
assertThat com.google.common.truth.Truth  Boolean  drawing.jaraappskids.kidsdrawing  
BrushToolTest  drawing.jaraappskids.kidsdrawing  EraserToolTest  drawing.jaraappskids.kidsdrawing  ExampleUnitTest  drawing.jaraappskids.kidsdrawing  Int  drawing.jaraappskids.kidsdrawing  KidsDrawingTestSuite  drawing.jaraappskids.kidsdrawing  Long  drawing.jaraappskids.kidsdrawing  Suite  drawing.jaraappskids.kidsdrawing  UndoRedoSystemTest  drawing.jaraappskids.kidsdrawing  WTDrawingViewTest  drawing.jaraappskids.kidsdrawing  drwaing  drawing.jaraappskids.kidsdrawing  Test 0drawing.jaraappskids.kidsdrawing.ExampleUnitTest  Boolean 5drawing.jaraappskids.kidsdrawing.KidsDrawingTestSuite  Int 5drawing.jaraappskids.kidsdrawing.KidsDrawingTestSuite  Long 5drawing.jaraappskids.kidsdrawing.KidsDrawingTestSuite  TestConfiguration 5drawing.jaraappskids.kidsdrawing.KidsDrawingTestSuite  Boolean ?drawing.jaraappskids.kidsdrawing.KidsDrawingTestSuite.Companion  Int ?drawing.jaraappskids.kidsdrawing.KidsDrawingTestSuite.Companion  Long ?drawing.jaraappskids.kidsdrawing.KidsDrawingTestSuite.Companion  TestConfiguration ?drawing.jaraappskids.kidsdrawing.KidsDrawingTestSuite.Companion  Boolean Gdrawing.jaraappskids.kidsdrawing.KidsDrawingTestSuite.TestConfiguration  Int Gdrawing.jaraappskids.kidsdrawing.KidsDrawingTestSuite.TestConfiguration  Long Gdrawing.jaraappskids.kidsdrawing.KidsDrawingTestSuite.TestConfiguration  BaseDrawingTest %drawing.jaraappskids.kidsdrawing.base  Boolean %drawing.jaraappskids.kidsdrawing.base  Float %drawing.jaraappskids.kidsdrawing.base  Int %drawing.jaraappskids.kidsdrawing.base  Long %drawing.jaraappskids.kidsdrawing.base  RobolectricTestRunner %drawing.jaraappskids.kidsdrawing.base  Unit %drawing.jaraappskids.kidsdrawing.base  
WTDrawingView %drawing.jaraappskids.kidsdrawing.base  
mutableListOf %drawing.jaraappskids.kidsdrawing.base  After 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  Before 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  Bitmap 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  Boolean 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  Context 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  Float 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  Int 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  Long 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  Test 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  	TestUtils 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  Unit 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  
WTDrawingView 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  getMUTABLEListOf 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  getMutableListOf 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  
mutableListOf 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  RobolectricTestRunner (drawing.jaraappskids.kidsdrawing.drawing  WTDrawingViewTest (drawing.jaraappskids.kidsdrawing.drawing  Test :drawing.jaraappskids.kidsdrawing.drawing.WTDrawingViewTest  RobolectricTestRunner )drawing.jaraappskids.kidsdrawing.features  UndoRedoSystemTest )drawing.jaraappskids.kidsdrawing.features  Test <drawing.jaraappskids.kidsdrawing.features.UndoRedoSystemTest  
BrushToolTest &drawing.jaraappskids.kidsdrawing.tools  EraserToolTest &drawing.jaraappskids.kidsdrawing.tools  RobolectricTestRunner &drawing.jaraappskids.kidsdrawing.tools  Test 4drawing.jaraappskids.kidsdrawing.tools.BrushToolTest  Test 5drawing.jaraappskids.kidsdrawing.tools.EraserToolTest  Bitmap &drawing.jaraappskids.kidsdrawing.utils  Boolean &drawing.jaraappskids.kidsdrawing.utils  Float &drawing.jaraappskids.kidsdrawing.utils  
FloatArray &drawing.jaraappskids.kidsdrawing.utils  Int &drawing.jaraappskids.kidsdrawing.utils  List &drawing.jaraappskids.kidsdrawing.utils  Long &drawing.jaraappskids.kidsdrawing.utils  Pair &drawing.jaraappskids.kidsdrawing.utils  	TestUtils &drawing.jaraappskids.kidsdrawing.utils  WTBezierPath &drawing.jaraappskids.kidsdrawing.utils  Bitmap 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  Boolean 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  DrawingViewState 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  Float 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  
FloatArray 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  Int 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  List 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  Long 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  
MemoryInfo 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  MotionEvent 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  Pair 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  View 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  WTBezierPath 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  Boolean Adrawing.jaraappskids.kidsdrawing.utils.TestUtils.DrawingViewState  Float Adrawing.jaraappskids.kidsdrawing.utils.TestUtils.DrawingViewState  Int Adrawing.jaraappskids.kidsdrawing.utils.TestUtils.DrawingViewState  Long Adrawing.jaraappskids.kidsdrawing.utils.TestUtils.DrawingViewState  Float ;drawing.jaraappskids.kidsdrawing.utils.TestUtils.MemoryInfo  Long ;drawing.jaraappskids.kidsdrawing.utils.TestUtils.MemoryInfo  
BrushToolTest 	java.lang  EraserToolTest 	java.lang  RobolectricTestRunner 	java.lang  Suite 	java.lang  UndoRedoSystemTest 	java.lang  WTBezierPath 	java.lang  
WTDrawingView 	java.lang  WTDrawingViewTest 	java.lang  
mutableListOf 	java.lang  Boolean kotlin  
BrushToolTest kotlin  EraserToolTest kotlin  Float kotlin  
FloatArray kotlin  Int kotlin  IntArray kotlin  Long kotlin  Nothing kotlin  Pair kotlin  RobolectricTestRunner kotlin  Suite kotlin  UndoRedoSystemTest kotlin  Unit kotlin  WTBezierPath kotlin  
WTDrawingView kotlin  WTDrawingViewTest kotlin  
intArrayOf kotlin  
mutableListOf kotlin  
BrushToolTest kotlin.annotation  EraserToolTest kotlin.annotation  Pair kotlin.annotation  RobolectricTestRunner kotlin.annotation  Suite kotlin.annotation  UndoRedoSystemTest kotlin.annotation  WTBezierPath kotlin.annotation  
WTDrawingView kotlin.annotation  WTDrawingViewTest kotlin.annotation  
mutableListOf kotlin.annotation  
BrushToolTest kotlin.collections  EraserToolTest kotlin.collections  List kotlin.collections  MutableList kotlin.collections  Pair kotlin.collections  RobolectricTestRunner kotlin.collections  Suite kotlin.collections  UndoRedoSystemTest kotlin.collections  WTBezierPath kotlin.collections  
WTDrawingView kotlin.collections  WTDrawingViewTest kotlin.collections  
mutableListOf kotlin.collections  
BrushToolTest kotlin.comparisons  EraserToolTest kotlin.comparisons  Pair kotlin.comparisons  RobolectricTestRunner kotlin.comparisons  Suite kotlin.comparisons  UndoRedoSystemTest kotlin.comparisons  WTBezierPath kotlin.comparisons  
WTDrawingView kotlin.comparisons  WTDrawingViewTest kotlin.comparisons  
mutableListOf kotlin.comparisons  
BrushToolTest 	kotlin.io  EraserToolTest 	kotlin.io  Pair 	kotlin.io  RobolectricTestRunner 	kotlin.io  Suite 	kotlin.io  UndoRedoSystemTest 	kotlin.io  WTBezierPath 	kotlin.io  
WTDrawingView 	kotlin.io  WTDrawingViewTest 	kotlin.io  
mutableListOf 	kotlin.io  
BrushToolTest 
kotlin.jvm  EraserToolTest 
kotlin.jvm  Pair 
kotlin.jvm  RobolectricTestRunner 
kotlin.jvm  Suite 
kotlin.jvm  UndoRedoSystemTest 
kotlin.jvm  WTBezierPath 
kotlin.jvm  
WTDrawingView 
kotlin.jvm  WTDrawingViewTest 
kotlin.jvm  
mutableListOf 
kotlin.jvm  Random 
kotlin.random  
BrushToolTest 
kotlin.ranges  EraserToolTest 
kotlin.ranges  Pair 
kotlin.ranges  RobolectricTestRunner 
kotlin.ranges  Suite 
kotlin.ranges  UndoRedoSystemTest 
kotlin.ranges  WTBezierPath 
kotlin.ranges  
WTDrawingView 
kotlin.ranges  WTDrawingViewTest 
kotlin.ranges  
mutableListOf 
kotlin.ranges  KClass kotlin.reflect  
BrushToolTest kotlin.sequences  EraserToolTest kotlin.sequences  Pair kotlin.sequences  RobolectricTestRunner kotlin.sequences  Suite kotlin.sequences  UndoRedoSystemTest kotlin.sequences  WTBezierPath kotlin.sequences  
WTDrawingView kotlin.sequences  WTDrawingViewTest kotlin.sequences  
mutableListOf kotlin.sequences  
BrushToolTest kotlin.text  EraserToolTest kotlin.text  Pair kotlin.text  RobolectricTestRunner kotlin.text  Suite kotlin.text  UndoRedoSystemTest kotlin.text  WTBezierPath kotlin.text  
WTDrawingView kotlin.text  WTDrawingViewTest kotlin.text  
mutableListOf kotlin.text  After 	org.junit  Assert 	org.junit  Before 	org.junit  Test 	org.junit  RunWith org.junit.runner  Suite org.junit.runners  SuiteClasses org.junit.runners.Suite  Mockito org.mockito  MockitoAnnotations org.mockito  RobolectricTestRunner org.robolectric  RuntimeEnvironment org.robolectric  Config org.robolectric.annotation  Path android.graphics  DrawingView 'drawing.jaraappskids.kidsdrawing.custom  Path &drawing.jaraappskids.kidsdrawing.utils  Path 0drawing.jaraappskids.kidsdrawing.utils.TestUtils  DrawingView 5drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest  AttributeSet android.util  core 
androidx.test  RobolectricTestRunner  drawing.jaraappskids.kidsdrawing  SimpleDrawingTest  drawing.jaraappskids.kidsdrawing  Test 2drawing.jaraappskids.kidsdrawing.SimpleDrawingTest                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  