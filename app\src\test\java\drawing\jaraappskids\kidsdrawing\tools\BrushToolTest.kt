package drawing.jaraappskids.kidsdrawing.tools

import android.graphics.Color
import com.google.common.truth.Truth.assertThat
import drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest
import drawing.jaraappskids.kidsdrawing.utils.TestUtils
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner

/**
 * Comprehensive tests for Brush Tool functionality
 * Tests color selection, size adjustment, smoothness control, and stroke persistence
 */
@RunWith(RobolectricTestRunner::class)
class BrushToolTest : BaseDrawingTest() {

    @Test
    fun `test brush color selection validation`() {
        // Given: A palette of test colors
        val testColors = listOf(
            Color.RED, Color.GREEN, Color.BLUE, Color.YELLOW,
            Color.MAGENTA, Color.CYAN, Color.BLACK, Color.WHITE,
            Color.GRAY, Color.LTGRAY, Color.DKGRAY
        )
        
        testColors.forEach { expectedColor ->
            // When: Setting brush color
            drawingView.setStrokeColor(expectedColor)
            
            // Then: Color should be set and retrievable
            assertThat(drawingView.getStrokeColor()).isEqualTo(expectedColor)
        }
    }

    @Test
    fun `test brush size adjustment within valid range`() {
        // Given: Valid brush size range (10-30dp)
        val minSize = 10f
        val maxSize = 30f
        val testSizes = floatArrayOf(10f, 15f, 20f, 25f, 30f)
        
        testSizes.forEach { size ->
            // When: Setting brush size
            drawingView.strokeWidth = size
            
            // Then: Size should be within valid range and set correctly
            assertThat(drawingView.strokeWidth).isEqualTo(size)
            assertThat(drawingView.strokeWidth).isAtLeast(minSize)
            assertThat(drawingView.strokeWidth).isAtMost(maxSize)
        }
    }

    @Test
    fun `test brush size boundary conditions`() {
        // Test minimum boundary
        drawingView.strokeWidth = 10f
        assertThat(drawingView.strokeWidth).isEqualTo(10f)
        
        // Test maximum boundary
        drawingView.strokeWidth = 30f
        assertThat(drawingView.strokeWidth).isEqualTo(30f)
        
        // Test values below minimum (should be handled gracefully)
        assertNoExceptions {
            drawingView.strokeWidth = 5f
        }
        
        // Test values above maximum (should be handled gracefully)
        assertNoExceptions {
            drawingView.strokeWidth = 50f
        }
    }

    @Test
    fun `test smoothness control with blur effects`() {
        // Given: Various blur values for smoothness
        val blurValues = intArrayOf(0, 5, 10, 15, 20, 25, 30)
        
        blurValues.forEach { blurValue ->
            // When: Setting blur for smoothness
            assertNoExceptions {
                drawingView.setBlurSize(blurValue)
            }
            
            // Then: Blur should be applied without errors
            // Note: Actual blur effect testing requires bitmap comparison
        }
    }

    @Test
    fun `test stroke width persistence during drawing`() {
        // Given: Specific brush configuration
        val testColor = Color.BLUE
        val testWidth = 15f
        
        setupDrawingView(testColor, testWidth, false)
        
        // When: Performing drawing operations
        simulateDrawing(10f, 10f, 50f, 50f)
        waitForDrawingCompletion()
        
        // Then: Brush settings should persist
        assertThat(drawingView.getStrokeColor()).isEqualTo(testColor)
        assertThat(drawingView.strokeWidth).isEqualTo(testWidth)
    }

    @Test
    fun `test brush mode vs pencil mode differences`() {
        // Test brush mode (default)
        drawingView.setPencilView(false)
        assertThat(drawingView.strokeWidth).isEqualTo(10f) // Default brush width
        
        // Test pencil mode
        drawingView.setPencilView(true)
        assertThat(drawingView.strokeWidth).isEqualTo(1f) // Thin pencil width
        
        // Switch back to brush mode
        drawingView.setPencilView(false)
        assertThat(drawingView.strokeWidth).isEqualTo(10f) // Back to brush width
    }

    @Test
    fun `test brush vs eraser mode switching`() {
        // Given: Brush in normal drawing mode
        drawingView.setEraserMode(false)
        assertThat(drawingView.isDrawingMode()).isFalse()
        
        // When: Switching to eraser mode
        drawingView.setEraserMode(true)
        
        // Then: Should be in eraser mode
        assertThat(drawingView.isDrawingMode()).isTrue()
        
        // When: Switching back to brush mode
        drawingView.setEraserMode(false)
        
        // Then: Should be back in normal drawing mode
        assertThat(drawingView.isDrawingMode()).isFalse()
    }

    @Test
    fun `test multiple brush strokes with different settings`() {
        // Given: Multiple brush configurations
        val brushConfigs = listOf(
            Triple(Color.RED, 12f, false),
            Triple(Color.GREEN, 18f, false),
            Triple(Color.BLUE, 25f, false),
            Triple(Color.YELLOW, 8f, false)
        )
        
        brushConfigs.forEachIndexed { index, (color, width, isEraser) ->
            // When: Applying brush configuration and drawing
            setupDrawingView(color, width, isEraser)
            
            val startX = (index * 20f) % 100f
            val startY = (index * 25f) % 100f
            simulateDrawing(startX, startY, startX + 30f, startY + 30f)
            
            // Then: Configuration should be maintained
            assertThat(drawingView.getStrokeColor()).isEqualTo(color)
            assertThat(drawingView.strokeWidth).isEqualTo(width)
            assertThat(drawingView.isDrawingMode()).isEqualTo(isEraser)
        }
    }

    @Test
    fun `test brush performance with rapid strokes`() {
        // Given: Brush configured for performance testing
        setupDrawingView(Color.BLACK, 15f, false)
        
        // When: Performing rapid brush strokes
        val executionTime = measurePerformance {
            repeat(30) { i ->
                val startX = (i * 5f) % 150f
                val startY = (i * 7f) % 150f
                simulateDrawing(startX, startY, startX + 20f, startY + 20f, 5)
            }
        }
        
        // Then: Performance should be acceptable (under 1.5 seconds for 30 strokes)
        assertThat(executionTime).isLessThan(1500L)
    }

    @Test
    fun `test brush memory usage during extended drawing`() {
        // Given: Initial memory measurement
        val initialMemory = TestUtils.measureMemoryUsage()
        
        // When: Performing extended drawing session
        repeat(25) { i ->
            val color = Color.rgb((i * 10) % 255, (i * 15) % 255, (i * 20) % 255)
            val width = 10f + (i % 15)
            
            setupDrawingView(color, width, false)
            simulateDrawing(
                (i * 6f) % 120f,
                (i * 8f) % 120f,
                ((i + 5) * 6f) % 120f,
                ((i + 5) * 8f) % 120f
            )
        }
        
        // Then: Memory usage should remain reasonable
        val finalMemory = TestUtils.measureMemoryUsage()
        val memoryIncrease = finalMemory.usedMemory - initialMemory.usedMemory
        val maxAllowedIncrease = 20 * 1024 * 1024 // 20MB
        
        assertThat(memoryIncrease).isLessThan(maxAllowedIncrease.toLong())
    }

    @Test
    fun `test brush color accuracy with custom colors`() {
        // Given: Custom color values
        val customColors = intArrayOf(
            Color.argb(255, 128, 64, 192),  // Custom purple
            Color.argb(200, 255, 128, 0),   // Semi-transparent orange
            Color.argb(150, 0, 255, 128),   // Semi-transparent green
            Color.argb(100, 255, 0, 255)    // Semi-transparent magenta
        )
        
        customColors.forEach { customColor ->
            // When: Setting custom color
            drawingView.setStrokeColor(customColor)
            
            // Then: Color should be set exactly as specified
            assertThat(drawingView.getStrokeColor()).isEqualTo(customColor)
        }
    }

    @Test
    fun `test brush state consistency after undo operations`() {
        // Given: Brush with specific settings
        val originalColor = Color.CYAN
        val originalWidth = 20f
        
        setupDrawingView(originalColor, originalWidth, false)
        simulateDrawing()
        
        // When: Performing undo operation
        drawingView.undo()
        
        // Then: Brush settings should remain unchanged
        assertThat(drawingView.getStrokeColor()).isEqualTo(originalColor)
        assertThat(drawingView.strokeWidth).isEqualTo(originalWidth)
        assertThat(drawingView.isDrawingMode()).isFalse()
    }

    @Test
    fun `test brush clear mask filter functionality`() {
        // Given: Brush with blur effect applied
        setupDrawingView(Color.RED, 15f, false)
        drawingView.setBlurSize(20)
        
        // When: Clearing mask filter
        assertNoExceptions {
            drawingView.clearMaskFilter()
        }
        
        // Then: Operation should complete without errors
        // Note: Visual verification would require bitmap comparison
    }
}
