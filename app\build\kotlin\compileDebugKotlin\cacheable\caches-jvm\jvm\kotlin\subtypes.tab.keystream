(androidx.appcompat.app.AppCompatActivity?drawing.jaraappskids.kidsdrawing.interfaces.AdapterItemCallback!android.view.View.OnClickListener7drawing.jaraappskids.kidsdrawing.interfaces.AdsCallbackandroid.os.AsyncTaskCdrawing.jaraappskids.kidsdrawing.interfaces.AdapterItemTypeCallback!android.view.View.OnTouchListenerEcom.myandroid.views.ScaleGestureDetector.SimpleOnScaleGestureListener<drawing.jaraappskids.kidsdrawing.interfaces.CallbackListener1androidx.recyclerview.widget.RecyclerView.Adapter4androidx.recyclerview.widget.RecyclerView.ViewHolder+androidx.lifecycle.DefaultLifecycleObserverandroid.app.Applicationandroid.view.View!androidx.cardview.widget.CardView,androidx.appcompat.widget.AppCompatImageViewandroid.graphics.Pathandroidx.fragment.app.Fragmentkotlin.Enum.android.widget.AdapterView.OnItemClickListenerandroid.widget.BaseAdapterandroid.os.Parcelable                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  