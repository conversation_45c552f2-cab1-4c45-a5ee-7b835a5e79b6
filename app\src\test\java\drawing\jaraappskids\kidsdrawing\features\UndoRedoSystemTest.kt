package drawing.jaraappskids.kidsdrawing.features

import android.graphics.Color
import com.google.common.truth.Truth.assertThat
import drawing.jaraappskids.kidsdrawing.base.BaseDrawingTest
import drawing.jaraappskids.kidsdrawing.utils.TestUtils
import org.junit.Test
import org.junit.runner.RunWith
import org.robolectric.RobolectricTestRunner

/**
 * Comprehensive tests for Undo/Redo System
 * Tests mixed tool usage scenarios, stack management, and state consistency
 */
@RunWith(RobolectricTestRunner::class)
class UndoRedoSystemTest : BaseDrawingTest() {

    @Test
    fun `test basic undo functionality`() {
        // Given: Canvas with a drawing operation
        setupDrawingView(Color.RED, 12f, false)
        simulateDrawing(10f, 10f, 50f, 50f)
        waitForDrawingCompletion()
        
        // When: Performing undo
        assertNoExceptions {
            drawingView.undo()
        }
        
        // Then: Undo should complete without errors
    }

    @Test
    fun `test basic redo functionality`() {
        // Given: Canvas with drawing and undo performed
        setupDrawingView(Color.BLUE, 15f, false)
        simulateDrawing(20f, 20f, 60f, 60f)
        drawingView.undo()
        
        // When: Performing redo
        assertNoExceptions {
            drawingView.redo()
        }
        
        // Then: Redo should complete without errors
    }

    @Test
    fun `test multiple undo operations in sequence`() {
        // Given: Canvas with multiple drawing operations
        val operations = listOf(
            Triple(Color.RED, 10f, false),
            Triple(Color.GREEN, 15f, false),
            Triple(Color.BLUE, 20f, false),
            Triple(Color.YELLOW, 12f, false)
        )
        
        operations.forEachIndexed { index, (color, width, isEraser) ->
            setupDrawingView(color, width, isEraser)
            simulateDrawing(
                (index * 20f) % 100f,
                (index * 25f) % 100f,
                ((index + 1) * 20f) % 100f,
                ((index + 1) * 25f) % 100f
            )
        }
        
        // When: Performing multiple undos
        repeat(operations.size) {
            assertNoExceptions {
                drawingView.undo()
            }
        }
        
        // Then: All undo operations should complete without errors
    }

    @Test
    fun `test multiple redo operations in sequence`() {
        // Given: Canvas with operations and multiple undos performed
        repeat(4) { i ->
            setupDrawingView(Color.rgb(i * 60, (i * 80) % 255, (i * 100) % 255), 10f + i * 3f, false)
            simulateDrawing(i * 15f, i * 20f, (i + 1) * 15f, (i + 1) * 20f)
        }
        
        // Undo all operations
        repeat(4) { drawingView.undo() }
        
        // When: Performing multiple redos
        repeat(4) {
            assertNoExceptions {
                drawingView.redo()
            }
        }
        
        // Then: All redo operations should complete without errors
    }

    @Test
    fun `test mixed tool usage with undo and redo`() {
        // Given: Mixed tool operations (brush → eraser → brush)
        
        // Brush operation
        setupDrawingView(Color.MAGENTA, 14f, false)
        simulateDrawing(10f, 10f, 40f, 40f)
        
        // Eraser operation
        setupDrawingView(Color.MAGENTA, 18f, true)
        simulateDrawing(25f, 25f, 55f, 55f)
        
        // Another brush operation
        setupDrawingView(Color.CYAN, 16f, false)
        simulateDrawing(45f, 45f, 75f, 75f)
        
        // When: Performing undo operations
        assertNoExceptions {
            drawingView.undo() // Undo brush
            drawingView.undo() // Undo eraser
            drawingView.undo() // Undo first brush
        }
        
        // When: Performing redo operations
        assertNoExceptions {
            drawingView.redo() // Redo first brush
            drawingView.redo() // Redo eraser
            drawingView.redo() // Redo second brush
        }
        
        // Then: All operations should complete without errors
    }

    @Test
    fun `test undo redo with rapid tool switching`() {
        // Given: Rapid tool switching scenario
        val toolConfigs = listOf(
            Triple(Color.RED, 8f, false),
            Triple(Color.RED, 20f, true),    // Eraser
            Triple(Color.GREEN, 12f, false),
            Triple(Color.GREEN, 25f, true),  // Eraser
            Triple(Color.BLUE, 16f, false),
            Triple(Color.BLUE, 15f, true)    // Eraser
        )
        
        toolConfigs.forEachIndexed { index, (color, width, isEraser) ->
            setupDrawingView(color, width, isEraser)
            simulateDrawing(
                (index * 12f) % 80f,
                (index * 15f) % 80f,
                ((index + 2) * 12f) % 80f,
                ((index + 2) * 15f) % 80f
            )
        }
        
        // When: Performing undo/redo cycles
        repeat(3) {
            assertNoExceptions {
                drawingView.undo()
            }
        }
        
        repeat(2) {
            assertNoExceptions {
                drawingView.redo()
            }
        }
        
        assertNoExceptions {
            drawingView.undo()
        }
        
        // Then: All operations should maintain consistency
    }

    @Test
    fun `test undo redo stack limits and behavior`() {
        // Given: Many operations to test stack limits
        repeat(50) { i ->
            setupDrawingView(
                Color.rgb((i * 5) % 255, (i * 7) % 255, (i * 11) % 255),
                10f + (i % 15),
                i % 4 == 0 // Every 4th operation is eraser
            )
            simulateDrawing(
                (i * 3f) % 100f,
                (i * 4f) % 100f,
                ((i + 5) * 3f) % 100f,
                ((i + 5) * 4f) % 100f
            )
        }
        
        // When: Performing many undo operations
        repeat(60) { // More undos than operations
            assertNoExceptions {
                drawingView.undo()
            }
        }
        
        // When: Performing many redo operations
        repeat(60) { // More redos than possible
            assertNoExceptions {
                drawingView.redo()
            }
        }
        
        // Then: Should handle gracefully without errors
    }

    @Test
    fun `test undo redo performance with complex operations`() {
        // Given: Complex drawing scenario
        createComplexDrawingScenario()
        
        // Add more complex operations
        repeat(10) { i ->
            setupDrawingView(
                Color.argb(200, (i * 25) % 255, (i * 35) % 255, (i * 45) % 255),
                8f + (i % 12),
                i % 3 == 0
            )
            
            // Create complex path
            val points = TestUtils.createComplexDrawingPattern()
            points.take(20).forEachIndexed { index, (x, y) ->
                val action = when (index) {
                    0 -> android.view.MotionEvent.ACTION_DOWN
                    19 -> android.view.MotionEvent.ACTION_UP
                    else -> android.view.MotionEvent.ACTION_MOVE
                }
                val event = TestUtils.createMockMotionEvent(action, x, y)
                drawingView.onTouchEvent(event)
            }
        }
        
        // When: Measuring undo/redo performance
        val undoTime = measurePerformance {
            repeat(15) { drawingView.undo() }
        }
        
        val redoTime = measurePerformance {
            repeat(10) { drawingView.redo() }
        }
        
        // Then: Performance should be acceptable
        assertThat(undoTime).isLessThan(1000L) // Under 1 second for 15 undos
        assertThat(redoTime).isLessThan(800L)  // Under 0.8 seconds for 10 redos
    }

    @Test
    fun `test undo redo memory management`() {
        // Given: Initial memory state
        val initialMemory = TestUtils.measureMemoryUsage()
        
        // Create operations that will be undone/redone
        repeat(20) { i ->
            setupDrawingView(
                Color.rgb((i * 12) % 255, (i * 18) % 255, (i * 24) % 255),
                10f + (i % 10),
                i % 5 == 0
            )
            simulateDrawing(
                (i * 8f) % 120f,
                (i * 10f) % 120f,
                ((i + 3) * 8f) % 120f,
                ((i + 3) * 10f) % 120f
            )
        }
        
        // When: Performing undo/redo cycles
        repeat(3) {
            // Undo several operations
            repeat(10) { drawingView.undo() }
            
            // Redo some operations
            repeat(7) { drawingView.redo() }
            
            // Undo again
            repeat(5) { drawingView.undo() }
        }
        
        // Then: Memory usage should remain reasonable
        val finalMemory = TestUtils.measureMemoryUsage()
        val memoryIncrease = finalMemory.usedMemory - initialMemory.usedMemory
        val maxAllowedIncrease = 25 * 1024 * 1024 // 25MB
        
        assertThat(memoryIncrease).isLessThan(maxAllowedIncrease.toLong())
    }

    @Test
    fun `test undo redo state consistency`() {
        // Given: Specific drawing state
        val originalColor = Color.DKGRAY
        val originalWidth = 18f
        val originalEraserMode = false
        
        setupDrawingView(originalColor, originalWidth, originalEraserMode)
        simulateDrawing(30f, 30f, 70f, 70f)
        
        // When: Performing undo and checking state
        drawingView.undo()
        
        // Then: Drawing view state should remain consistent
        assertThat(drawingView.getStrokeColor()).isEqualTo(originalColor)
        assertThat(drawingView.strokeWidth).isEqualTo(originalWidth)
        assertThat(drawingView.isDrawingMode()).isEqualTo(originalEraserMode)
        
        // When: Performing redo and checking state
        drawingView.redo()
        
        // Then: State should still be consistent
        assertThat(drawingView.getStrokeColor()).isEqualTo(originalColor)
        assertThat(drawingView.strokeWidth).isEqualTo(originalWidth)
        assertThat(drawingView.isDrawingMode()).isEqualTo(originalEraserMode)
    }

    @Test
    fun `test undo redo with clear operations`() {
        // Given: Canvas with multiple operations
        createComplexDrawingScenario()
        
        // When: Clearing canvas
        drawingView.clear()
        
        // When: Attempting undo after clear
        assertNoExceptions {
            drawingView.undo()
        }
        
        // When: Attempting redo after clear
        assertNoExceptions {
            drawingView.redo()
        }
        
        // Then: Operations should handle gracefully
    }
}
