{"logs": [{"outputFile": "drawing.jaraappskids.kidsdrawing.app-mergeDebugResources-64:/values-sq/values-sq.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\4886f4714567b788bedb48de3c472517\\transformed\\appcompat-1.7.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,219,319,431,517,623,746,828,906,997,1090,1185,1279,1380,1473,1568,1665,1756,1849,1930,2036,2140,2238,2344,2448,2550,2704,2801", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "214,314,426,512,618,741,823,901,992,1085,1180,1274,1375,1468,1563,1660,1751,1844,1925,2031,2135,2233,2339,2443,2545,2699,2796,2878"}, "to": {"startLines": "6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,163", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "317,431,531,643,729,835,958,1040,1118,1209,1302,1397,1491,1592,1685,1780,1877,1968,2061,2142,2248,2352,2450,2556,2660,2762,2916,14682", "endColumns": "113,99,111,85,105,122,81,77,90,92,94,93,100,92,94,96,90,92,80,105,103,97,105,103,101,153,96,81", "endOffsets": "426,526,638,724,830,953,1035,1113,1204,1297,1392,1486,1587,1680,1775,1872,1963,2056,2137,2243,2347,2445,2551,2655,2757,2911,3008,14759"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\f8992bbb99f27b442a756634e186b35a\\transformed\\jetified-tv-ads-1.0.0\\res\\values-sq\\values.xml", "from": {"startLines": "4,5,6,9", "startColumns": "0,0,0,0", "startOffsets": "197,244,366,593", "endColumns": "46,121,59,73", "endOffsets": "243,365,425,666"}, "to": {"startLines": "33,71,72,75", "startColumns": "4,4,4,4", "startOffsets": "3013,7201,7327,7583", "endColumns": "50,125,63,77", "endOffsets": "3059,7322,7386,7656"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\6a58776e9dcdced13d931fd31f07cadf\\transformed\\jetified-play-services-ads-24.3.0\\res\\values-sq\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "199,242,290,347,409,475,577,640,767,872,982,1040,1090,1209,1296,1337,1434,1467,1501,1558,1643,1683", "endColumns": "42,47,56,61,65,101,62,126,104,109,57,49,118,86,40,96,32,33,56,84,39,55", "endOffsets": "241,289,346,408,474,576,639,766,871,981,1039,1089,1208,1295,1336,1433,1466,1500,1557,1642,1682,1738"}, "to": {"startLines": "139,140,141,143,144,145,146,147,148,149,150,151,152,153,156,157,158,159,160,161,162,171", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12799,12846,12898,13043,13109,13179,13285,13352,13483,13592,13706,13768,13822,13945,14267,14312,14413,14450,14488,14549,14638,15434", "endColumns": "46,51,60,65,69,105,66,130,108,113,61,53,122,90,44,100,36,37,60,88,43,59", "endOffsets": "12841,12893,12954,13104,13174,13280,13347,13478,13587,13701,13763,13817,13940,14031,14307,14408,14445,14483,14544,14633,14677,15489"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c3a5dd56922f0d9d5fe7ff90c31db109\\transformed\\core-1.13.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,154,256,354,451,559,670,792", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "149,251,349,446,554,665,787,888"}, "to": {"startLines": "39,40,41,42,43,44,45,167", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "3499,3598,3700,3798,3895,4003,4114,15001", "endColumns": "98,101,97,96,107,110,121,100", "endOffsets": "3593,3695,3793,3890,3998,4109,4231,15097"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\8d70832d7f1deb228721870d2bb07788\\transformed\\jetified-play-services-base-18.5.0\\res\\values-sq\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,296,465,598,701,858,988,1110,1222,1388,1492,1663,1797,1955,2135,2196,2259", "endColumns": "102,168,132,102,156,129,121,111,165,103,170,133,157,179,60,62,77", "endOffsets": "295,464,597,700,857,987,1109,1221,1387,1491,1662,1796,1954,2134,2195,2258,2336"}, "to": {"startLines": "49,50,51,52,53,54,55,56,58,59,60,61,62,63,64,65,66", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "4546,4653,4826,4963,5070,5231,5365,5491,5736,5906,6014,6189,6327,6489,6673,6738,6805", "endColumns": "106,172,136,106,160,133,125,115,169,107,174,137,161,183,64,66,81", "endOffsets": "4648,4821,4958,5065,5226,5360,5486,5602,5901,6009,6184,6322,6484,6668,6733,6800,6882"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\acab93b1b19a0a7fc9f8e2a900142b63\\transformed\\jetified-play-services-basement-18.4.0\\res\\values-sq\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "124", "endOffsets": "319"}, "to": {"startLines": "57", "startColumns": "4", "startOffsets": "5607", "endColumns": "128", "endOffsets": "5731"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\c4e3970f58cff9116df172ae1e89ec5d\\transformed\\preference-1.2.1\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,174,266,350,498,667,751", "endColumns": "68,91,83,147,168,83,78", "endOffsets": "169,261,345,493,662,746,825"}, "to": {"startLines": "67,73,142,155,168,169,170", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "6887,7391,12959,14119,15102,15271,15355", "endColumns": "68,91,83,147,168,83,78", "endOffsets": "6951,7478,13038,14262,15266,15350,15429"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\ac185097d874e47ec4cafd044e6cc8ed\\transformed\\browser-1.8.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,3,4,5", "startColumns": "4,4,4,4", "startOffsets": "55,170,271,382", "endColumns": "114,100,110,100", "endOffsets": "165,266,377,478"}, "to": {"startLines": "68,76,77,78", "startColumns": "4,4,4,4", "startOffsets": "6956,7661,7762,7873", "endColumns": "114,100,110,100", "endOffsets": "7066,7757,7868,7969"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\transforms-4\\56b21b1e2df5e7a9e2bbd796d8a365f1\\transformed\\material-1.12.0\\res\\values-sq\\values-sq.xml", "from": {"startLines": "2,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,267,346,424,510,610,702,803,929,1012,1077,1142,1242,1312,1371,1469,1531,1595,1654,1726,1789,1843,1960,2017,2079,2133,2205,2340,2423,2502,2598,2681,2759,2900,2984,3066,3214,3304,3382,3435,3494,3560,3631,3710,3781,3864,3940,4018,4090,4163,4267,4356,4428,4522,4621,4695,4767,4868,4918,5003,5069,5159,5248,5310,5374,5437,5504,5620,5733,5842,5947,6004,6067,6150,6235,6309", "endLines": "5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80", "endColumns": "12,78,77,85,99,91,100,125,82,64,64,99,69,58,97,61,63,58,71,62,53,116,56,61,53,71,134,82,78,95,82,77,140,83,81,147,89,77,52,58,65,70,78,70,82,75,77,71,72,103,88,71,93,98,73,71,100,49,84,65,89,88,61,63,62,66,115,112,108,104,56,62,82,84,73,77", "endOffsets": "262,341,419,505,605,697,798,924,1007,1072,1137,1237,1307,1366,1464,1526,1590,1649,1721,1784,1838,1955,2012,2074,2128,2200,2335,2418,2497,2593,2676,2754,2895,2979,3061,3209,3299,3377,3430,3489,3555,3626,3705,3776,3859,3935,4013,4085,4158,4262,4351,4423,4517,4616,4690,4762,4863,4913,4998,5064,5154,5243,5305,5369,5432,5499,5615,5728,5837,5942,5999,6062,6145,6230,6304,6382"}, "to": {"startLines": "2,34,35,36,37,38,46,47,48,69,70,74,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,154,164,165,166", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3064,3143,3221,3307,3407,4236,4337,4463,7071,7136,7483,7974,8044,8103,8201,8263,8327,8386,8458,8521,8575,8692,8749,8811,8865,8937,9072,9155,9234,9330,9413,9491,9632,9716,9798,9946,10036,10114,10167,10226,10292,10363,10442,10513,10596,10672,10750,10822,10895,10999,11088,11160,11254,11353,11427,11499,11600,11650,11735,11801,11891,11980,12042,12106,12169,12236,12352,12465,12574,12679,12736,14036,14764,14849,14923", "endLines": "5,34,35,36,37,38,46,47,48,69,70,74,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,154,164,165,166", "endColumns": "12,78,77,85,99,91,100,125,82,64,64,99,69,58,97,61,63,58,71,62,53,116,56,61,53,71,134,82,78,95,82,77,140,83,81,147,89,77,52,58,65,70,78,70,82,75,77,71,72,103,88,71,93,98,73,71,100,49,84,65,89,88,61,63,62,66,115,112,108,104,56,62,82,84,73,77", "endOffsets": "312,3138,3216,3302,3402,3494,4332,4458,4541,7131,7196,7578,8039,8098,8196,8258,8322,8381,8453,8516,8570,8687,8744,8806,8860,8932,9067,9150,9229,9325,9408,9486,9627,9711,9793,9941,10031,10109,10162,10221,10287,10358,10437,10508,10591,10667,10745,10817,10890,10994,11083,11155,11249,11348,11422,11494,11595,11645,11730,11796,11886,11975,12037,12101,12164,12231,12347,12460,12569,12674,12731,12794,14114,14844,14918,14996"}}]}]}