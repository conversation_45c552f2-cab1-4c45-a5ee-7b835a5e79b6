package drawing.jaraappskids.kidsdrawing.adapters

import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentActivity
import androidx.viewpager2.adapter.FragmentStateAdapter
import drawing.jaraappskids.kidsdrawing.fragments.StickerCategoryFragment
import drawing.jaraappskids.kidsdrawing.pojo.StickerClass

class StickerCategoryPagerAdapter(
    fragmentActivity: FragmentActivity,
    private val stickerCategories: Map<String, ArrayList<StickerClass>>
) : FragmentStateAdapter(fragmentActivity) {

    private val categoryNames = stickerCategories.keys.toList()

    override fun getItemCount(): Int = stickerCategories.size

    override fun createFragment(position: Int): Fragment {
        val categoryName = categoryNames[position]
        val stickersInCategory = stickerCategories[categoryName] ?: ArrayList()
        return StickerCategoryFragment.newInstance(stickersInCategory)
    }

    fun getCategoryName(position: Int): String {
        return if (position in categoryNames.indices) {
            categoryNames[position]
        } else {
            ""
        }
    }
}
